import * as vscode from 'vscode';
import { ServiceRegistry } from '../core/ServiceRegistry';
import { Logger } from '../utils/Logger';
import { AIService } from '../services/AIService';
import { ConfigurationService } from '../services/ConfigurationService';
import { ProjectDetectionService } from '../services/ProjectDetectionService';
import { UsageTrackingService } from '../services/UsageTrackingService';
import { WorkspaceAnalysisService } from '../services/WorkspaceAnalysisService';

/**
 * Chat webview provider for UIOrbit extension
 * Provides the main chat interface for user interaction
 */
export class ChatWebviewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'uiorbit.chatView';

  private _view?: vscode.WebviewView;
  private aiService: AIService;

  constructor(
    private readonly _extensionUri: vscode.Uri,
    private readonly serviceRegistry: ServiceRegistry
  ) {
    // Initialize AI service
    const configService = this.serviceRegistry.getRequired<ConfigurationService>('configuration');
    this.aiService = new AIService(configService);

    // Inject project detection service if available
    const projectDetectionService = this.serviceRegistry.get<ProjectDetectionService>('projectDetection');
    if (projectDetectionService) {
      this.aiService.setProjectDetectionService(projectDetectionService);
    }
  }

  /**
   * Resolve the webview view
   */
  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    Logger.info('ChatWebviewProvider.resolveWebviewView called');
    this._view = webviewView;

    webviewView.webview.options = {
      // Allow scripts in the webview
      enableScripts: true,
      localResourceRoots: [
        this._extensionUri
      ]
    };

    const html = this._getHtmlForWebview(webviewView.webview);
    Logger.info('Setting webview HTML, length:', html.length);
    webviewView.webview.html = html;

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(
      message => {
        Logger.info('Received message from webview:', message);
        this.handleMessage(message);
      },
      undefined,
      []
    );

    // Send welcome message after a delay to ensure React app is loaded
    setTimeout(() => {
      Logger.info('Sending welcome message to webview');
      this.postMessage({
        type: 'welcome',
        text: 'Welcome to UIOrbit! How can I help you with frontend development today?'
      });
    }, 2000);

    Logger.info('Chat webview resolved successfully');
  }

  /**
   * Handle messages from the webview
   */
  private async handleMessage(message: any): Promise<void> {
    try {
      Logger.debug('Received message from webview:', message);

      switch (message.type) {
        case 'chat-message':
          await this.handleChatMessage(message.text, message.attachments);
          break;
        case 'file-attachment':
          await this.handleFileAttachment(message.file);
          break;
        case 'get-mention-suggestions':
          await this.handleGetMentionSuggestions(message.query);
          break;
        case 'ready':
          await this.handleWebviewReady();
          break;
        case 'webview-loaded':
          Logger.info('Webview loaded successfully');
          break;
        case 'get-usage-stats':
          await this.handleGetUsageStats();
          break;
        case 'show-usage-details':
          await this.handleShowUsageDetails();
          break;
        case 'error':
          Logger.error('Webview error:', message.error);
          break;
        default:
          Logger.warn('Unknown message type:', message.type);
      }
    } catch (error) {
      Logger.error('Error handling webview message:', error);
    }
  }

  /**
   * Handle chat message from user
   */
  private async handleChatMessage(text: string, attachments?: any[]): Promise<void> {
    if (!text || text.trim().length === 0) {
      return;
    }

    Logger.info('Processing chat message:', text);
    if (attachments && attachments.length > 0) {
      Logger.info('Message has attachments:', attachments.length);
    }

    // Send typing indicator
    this.postMessage({
      type: 'assistant-typing',
      isTyping: true
    });

    try {
      // Check if AI service is ready
      if (!this.aiService.isReady()) {
        // Send configuration message
        this.postMessage({
          type: 'assistant-message',
          text: '🔧 **AI Configuration Required**\n\nTo enable intelligent responses, please configure your OpenAI API key:\n\n1. Open VS Code Settings (`Ctrl+,` or `Cmd+,`)\n2. Search for "UIOrbit"\n3. Enter your OpenAI API key in **"UIOrbit > OpenAI API Key"**\n\nOnce configured, I\'ll be able to provide expert frontend development assistance! 🚀',
          timestamp: new Date().toISOString(),
          isError: true
        });
        return;
      }

      // Process message with AI service
      const aiResponse = await this.aiService.processMessage(text);

      // Send response
      this.postMessage({
        type: 'assistant-message',
        text: aiResponse.content,
        timestamp: new Date().toISOString(),
        isError: aiResponse.isError
      });

      // Log usage if available
      if (aiResponse.usage) {
        Logger.info(`AI Response - Tokens used: ${aiResponse.usage.totalTokens} (prompt: ${aiResponse.usage.promptTokens}, completion: ${aiResponse.usage.completionTokens})`);
      }

      // Update usage stats in UI
      await this.handleGetUsageStats();

    } catch (error) {
      Logger.error('Error processing chat message:', error);
      
      this.postMessage({
        type: 'assistant-message',
        text: 'Sorry, I encountered an error processing your message. Please try again.',
        timestamp: new Date().toISOString(),
        isError: true
      });
    } finally {
      // Stop typing indicator
      this.postMessage({
        type: 'assistant-typing',
        isTyping: false
      });
    }
  }

  /**
   * Handle webview ready event
   */
  private async handleWebviewReady(): Promise<void> {
    Logger.info('Webview is ready');

    // Update project context
    await this.aiService.updateProjectContext();
    const projectContext = this.aiService.getProjectContext();

    // Generate context-aware welcome message
    let welcomeMessage = 'Hello! I\'m UIOrbit, your AI-powered frontend development assistant. ';

    if (projectContext.projectType && projectContext.projectType !== 'unknown' && projectContext.projectType !== 'none') {
      welcomeMessage += `I can see you're working on a ${projectContext.projectType} project. `;
    }

    welcomeMessage += 'I can help you with:\n\n';
    welcomeMessage += '• 🎨 Component design and architecture\n';
    welcomeMessage += '• 🚀 Modern UI/UX best practices\n';
    welcomeMessage += '• 📱 Responsive design solutions\n';
    welcomeMessage += '• ⚡ Performance optimization\n';
    welcomeMessage += '• 🎭 Animations and interactions\n';
    welcomeMessage += '• 🔧 Debugging and troubleshooting\n\n';
    welcomeMessage += 'What would you like to work on today?';

    // Send welcome message
    this.postMessage({
      type: 'assistant-message',
      text: welcomeMessage,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle get usage stats request
   */
  private async handleGetUsageStats(): Promise<void> {
    try {
      const usageService = this.serviceRegistry.get<UsageTrackingService>('usageTracking');
      if (usageService) {
        const stats = await usageService.getUsageStats();
        this.postMessage({
          type: 'usage-stats',
          stats: stats
        });
      }
    } catch (error) {
      Logger.error('Error getting usage stats:', error);
    }
  }

  /**
   * Handle show usage details request
   */
  private async handleShowUsageDetails(): Promise<void> {
    try {
      const usageService = this.serviceRegistry.get<UsageTrackingService>('usageTracking');
      if (usageService) {
        await usageService.showUsageStats();
      }
    } catch (error) {
      Logger.error('Error showing usage details:', error);
    }
  }

  /**
   * Handle file attachment from user
   */
  private async handleFileAttachment(file: { name: string; type: string; data?: string; content?: string }): Promise<void> {
    try {
      Logger.info('Processing file attachment:', file.name, file.type);

      // Validate file type
      const allowedTypes = [
        'image/png', 'image/jpeg', 'image/gif', 'image/webp', 'image/svg+xml',
        'text/plain', 'text/javascript', 'text/typescript', 'text/css', 'text/html',
        'application/json', 'application/javascript', 'application/typescript'
      ];

      if (!allowedTypes.includes(file.type) && !file.name.match(/\.(js|ts|jsx|tsx|css|html|json|md|txt)$/i)) {
        this.postMessage({
          type: 'assistant-message',
          text: '❌ **Unsupported File Type**\n\nSupported file types:\n- Images: PNG, JPEG, GIF, WebP, SVG\n- Code: JS, TS, JSX, TSX, CSS, HTML, JSON\n- Text: TXT, MD',
          timestamp: new Date().toISOString(),
          isError: true
        });
        return;
      }

      // Process file based on type
      if (file.type.startsWith('image/')) {
        await this.processImageAttachment(file);
      } else {
        await this.processCodeAttachment(file);
      }

    } catch (error) {
      Logger.error('Error processing file attachment:', error);
      this.postMessage({
        type: 'assistant-message',
        text: 'Sorry, I encountered an error processing your file attachment. Please try again.',
        timestamp: new Date().toISOString(),
        isError: true
      });
    }
  }

  /**
   * Process image attachment
   */
  private async processImageAttachment(file: { name: string; type: string; data?: string }): Promise<void> {
    this.postMessage({
      type: 'assistant-message',
      text: `🖼️ **Image Received: ${file.name}**\n\nI can help you:\n- Generate components from this design\n- Extract design tokens (colors, spacing, typography)\n- Create responsive layouts\n- Convert to React/Vue/Angular code\n\nWhat would you like me to do with this image?`,
      timestamp: new Date().toISOString(),
      attachment: {
        type: 'image',
        name: file.name,
        data: file.data
      }
    });
  }

  /**
   * Process code file attachment
   */
  private async processCodeAttachment(file: { name: string; type: string; content?: string }): Promise<void> {
    this.postMessage({
      type: 'assistant-message',
      text: `📄 **Code File Received: ${file.name}**\n\nI can help you:\n- Review and improve the code\n- Suggest modern patterns and best practices\n- Add TypeScript types\n- Generate tests\n- Optimize performance\n\nWhat would you like me to do with this code?`,
      timestamp: new Date().toISOString(),
      attachment: {
        type: 'code',
        name: file.name,
        content: file.content,
        language: this.detectLanguage(file.name)
      }
    });
  }

  /**
   * Detect programming language from file extension
   */
  private detectLanguage(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'css': 'css',
      'scss': 'scss',
      'html': 'html',
      'json': 'json',
      'md': 'markdown',
      'txt': 'text'
    };
    return languageMap[ext || ''] || 'text';
  }

  /**
   * Handle mention suggestions request
   */
  private async handleGetMentionSuggestions(query: string): Promise<void> {
    try {
      Logger.info('Getting mention suggestions for query:', query);

      // Get workspace files and symbols
      const suggestions = await this.getMentionSuggestions(query);

      this.postMessage({
        type: 'mention-suggestions',
        suggestions: suggestions
      });

    } catch (error) {
      Logger.error('Error getting mention suggestions:', error);
    }
  }

  /**
   * Get mention suggestions based on query
   */
  private async getMentionSuggestions(query: string): Promise<any[]> {
    const suggestions: any[] = [];
    const workspaceService = this.serviceRegistry.get<WorkspaceAnalysisService>('workspaceAnalysis');

    if (!workspaceService) {
      return suggestions;
    }

    try {
      // Get workspace files using VS Code API
      const workspaceFiles = await this.getWorkspaceFiles();

      // Filter files based on query
      const filteredFiles = workspaceFiles
        .filter((file: string) => {
          const fileName = file.split('/').pop() || '';
          const baseName = fileName.split('.')[0];
          return baseName.toLowerCase().includes(query.toLowerCase()) ||
                 fileName.toLowerCase().includes(query.toLowerCase());
        })
        .slice(0, 10); // Limit to 10 files

      // Add file suggestions
      filteredFiles.forEach((file: string) => {
        const fileName = file.split('/').pop() || '';
        suggestions.push({
          type: 'file',
          name: fileName.split('.')[0],
          path: file,
          fullPath: file
        });
      });

      // Add common component suggestions (mock data for now)
      if (query.toLowerCase().includes('button') || query === '') {
        suggestions.push({
          type: 'component',
          name: 'Button',
          path: 'components/Button.tsx',
          fullPath: 'src/components/Button.tsx'
        });
      }

      if (query.toLowerCase().includes('card') || query === '') {
        suggestions.push({
          type: 'component',
          name: 'Card',
          path: 'components/Card.tsx',
          fullPath: 'src/components/Card.tsx'
        });
      }

      if (query.toLowerCase().includes('modal') || query === '') {
        suggestions.push({
          type: 'component',
          name: 'Modal',
          path: 'components/Modal.tsx',
          fullPath: 'src/components/Modal.tsx'
        });
      }

      // Add function suggestions (mock data for now)
      if (query.toLowerCase().includes('use') || query === '') {
        suggestions.push({
          type: 'function',
          name: 'useState',
          path: 'React Hook',
          fullPath: 'react'
        });

        suggestions.push({
          type: 'function',
          name: 'useEffect',
          path: 'React Hook',
          fullPath: 'react'
        });
      }

      return suggestions.slice(0, 8); // Limit total suggestions

    } catch (error) {
      Logger.error('Error getting workspace files for mentions:', error);
      return suggestions;
    }
  }

  /**
   * Get workspace files for mention suggestions
   */
  private async getWorkspaceFiles(): Promise<string[]> {
    const files: string[] = [];

    if (vscode.workspace.workspaceFolders) {
      for (const folder of vscode.workspace.workspaceFolders) {
        const pattern = new vscode.RelativePattern(folder, '**/*.{ts,tsx,js,jsx,css,scss,html,json}');
        const foundFiles = await vscode.workspace.findFiles(pattern, '**/node_modules/**');

        files.push(...foundFiles.map(uri => uri.fsPath));
      }
    }

    return files;
  }

  /**
   * Post message to webview
   */
  private postMessage(message: any): void {
    if (this._view) {
      this._view.webview.postMessage(message);
    }
  }

  /**
   * Get HTML content for the webview with enhanced HTML/CSS/JS
   */
  private _getHtmlForWebview(webview: vscode.Webview): string {
    // Use enhanced HTML version - React was causing issues
    const useReact = false; // Keep it simple with enhanced HTML/CSS/JS

    if (!useReact) {
      // Enhanced HTML version with all Week 2 features
      return this._getEnhancedHtmlForWebview(webview);
    }

    // Get URIs for React bundle
    const scriptUri = webview.asWebviewUri(
      vscode.Uri.joinPath(this._extensionUri, 'dist', 'webview.js')
    );

    // Use a nonce to only allow specific scripts to be run
    const nonce = this.getNonce();

    Logger.info('Script URI:', scriptUri.toString());

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}' 'unsafe-eval';">
    <title>UIOrbit Chat</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
        }
        #root {
            height: 100vh;
        }
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">Loading UIOrbit Chat...</div>
    </div>
    <script nonce="${nonce}">
        console.log('UIOrbit webview script loading...');
        console.log('Script URI: ${scriptUri}');

        // Add error handling
        window.addEventListener('error', function(e) {
            console.error('UIOrbit webview error:', e.error);
        });

        // Check if VS Code API is available
        try {
            const vscode = acquireVsCodeApi();
            console.log('VS Code API acquired successfully');
            vscode.postMessage({ type: 'webview-loaded' });
        } catch (error) {
            console.error('Failed to acquire VS Code API:', error);
        }
    </script>
    <script nonce="${nonce}" src="${scriptUri}" onload="console.log('React script loaded successfully')" onerror="console.error('Failed to load React script')"></script>
</body>
</html>`;
  }

  /**
   * Get enhanced HTML content with Augment-level design
   */
  private _getEnhancedHtmlForWebview(webview: vscode.Webview): string {
    const nonce = this.getNonce();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
    <title>UIOrbit Chat</title>
    <style>
        /* Augment-inspired design system */
        :root {
            --uiorbit-primary: #007acc;
            --uiorbit-primary-hover: #005a9e;
            --uiorbit-secondary: #6c757d;
            --uiorbit-success: #28a745;
            --uiorbit-warning: #ffc107;
            --uiorbit-error: #dc3545;
            --uiorbit-border-radius: 8px;
            --uiorbit-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            --uiorbit-transition: all 0.2s ease;
        }

        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        /* Augment-style header */
        .header {
            background: linear-gradient(135deg, var(--vscode-sideBar-background) 0%, var(--vscode-editor-background) 100%);
            border-bottom: 1px solid var(--vscode-panel-border);
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 60px;
            box-shadow: var(--uiorbit-shadow);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--uiorbit-primary) 0%, #4a90e2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--vscode-foreground);
            margin: 0;
        }

        .header-subtitle {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin: 0;
            margin-top: 2px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
        }

        .usage-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            color: var(--vscode-foreground);
            cursor: pointer;
            transition: var(--uiorbit-transition);
        }

        .usage-indicator:hover {
            background-color: var(--vscode-list-hoverBackground);
        }

        .usage-bar {
            width: 40px;
            height: 4px;
            background-color: var(--vscode-input-border);
            border-radius: 2px;
            overflow: hidden;
        }

        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--uiorbit-success) 0%, var(--uiorbit-warning) 70%, var(--uiorbit-error) 100%);
            transition: width 0.3s ease;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--uiorbit-success);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Enhanced chat container */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 16px;
            overflow-y: auto;
            padding: 20px;
            background-color: var(--vscode-editor-background);
        }
        /* Augment-style messages */
        .message {
            padding: 16px 20px;
            border-radius: var(--uiorbit-border-radius);
            max-width: 85%;
            word-wrap: break-word;
            position: relative;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: var(--uiorbit-transition);
        }

        .message:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .user-message {
            background: linear-gradient(135deg, var(--uiorbit-primary) 0%, #4a90e2 100%);
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 4px;
        }

        .user-message::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: -8px;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-bottom-color: var(--uiorbit-primary);
            border-right: 0;
            border-bottom-right-radius: 4px;
        }

        .assistant-message {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            align-self: flex-start;
            border-bottom-left-radius: 4px;
        }

        .assistant-message::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: -8px;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-bottom-color: var(--vscode-input-background);
            border-left: 0;
            border-bottom-left-radius: 4px;
        }
        /* Augment-style input area */
        .input-container {
            background-color: var(--vscode-sideBar-background);
            border-top: 1px solid var(--vscode-panel-border);
            padding: 16px 20px;
            display: flex;
            gap: 12px;
            align-items: flex-end;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 2px solid var(--vscode-input-border);
            border-radius: var(--uiorbit-border-radius);
            padding: 12px 16px;
            font-family: inherit;
            font-size: inherit;
            resize: none;
            min-height: 20px;
            max-height: 120px;
            transition: var(--uiorbit-transition);
            box-sizing: border-box;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--uiorbit-primary);
            box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
        }

        .message-input::placeholder {
            color: var(--vscode-input-placeholderForeground);
            font-style: italic;
        }

        .send-button {
            background: linear-gradient(135deg, var(--uiorbit-primary) 0%, #4a90e2 100%);
            color: white;
            border: none;
            border-radius: var(--uiorbit-border-radius);
            padding: 12px 20px;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
            font-weight: 600;
            transition: var(--uiorbit-transition);
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 80px;
            justify-content: center;
        }

        .send-button:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--uiorbit-primary-hover) 0%, #3a7bc8 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
        }

        .send-button:active:not(:disabled) {
            transform: translateY(0);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .send-icon {
            font-size: 14px;
        }

        /* File attachment button */
        .attachment-button {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: 1px solid var(--vscode-button-border);
            border-radius: var(--uiorbit-border-radius);
            padding: 12px;
            cursor: pointer;
            font-size: 16px;
            transition: var(--uiorbit-transition);
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            height: 44px;
        }

        .attachment-button:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
            transform: translateY(-1px);
        }

        .attachment-button:active {
            transform: translateY(0);
        }

        .attachment-input {
            display: none;
        }

        /* Attachment preview */
        .attachment-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
            padding: 8px;
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: var(--uiorbit-border-radius);
        }

        .attachment-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 10px;
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            border-radius: 16px;
            font-size: 12px;
            max-width: 200px;
        }

        .attachment-item .file-icon {
            font-size: 14px;
        }

        .attachment-item .file-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .attachment-item .remove-btn {
            background: none;
            border: none;
            color: var(--vscode-badge-foreground);
            cursor: pointer;
            padding: 0;
            font-size: 12px;
            opacity: 0.7;
        }

        .attachment-item .remove-btn:hover {
            opacity: 1;
        }

        /* Message attachment display */
        .message-attachments {
            margin-bottom: 12px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .attachment-display {
            border: 1px solid var(--vscode-input-border);
            border-radius: var(--uiorbit-border-radius);
            padding: 8px;
            background-color: var(--vscode-input-background);
        }

        .attachment-display img {
            display: block;
            margin-bottom: 4px;
        }

        .attachment-name {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-top: 4px;
        }

        .code-attachment {
            font-family: var(--vscode-editor-font-family);
        }

        .attachment-header {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .attachment-code {
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
            line-height: 1.4;
            overflow-x: auto;
            margin: 0;
        }

        /* @ Mentions dropdown */
        .mention-dropdown {
            background-color: var(--vscode-dropdown-background);
            border: 1px solid var(--vscode-dropdown-border);
            border-radius: var(--uiorbit-border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            min-width: 250px;
        }

        .mention-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            cursor: pointer;
            transition: var(--uiorbit-transition);
            border-bottom: 1px solid var(--vscode-dropdown-border);
        }

        .mention-item:last-child {
            border-bottom: none;
        }

        .mention-item:hover,
        .mention-item.selected {
            background-color: var(--vscode-list-hoverBackground);
        }

        .mention-icon {
            font-size: 16px;
            flex-shrink: 0;
        }

        .mention-info {
            flex: 1;
            min-width: 0;
        }

        .mention-name {
            font-size: 13px;
            font-weight: 500;
            color: var(--vscode-foreground);
            margin-bottom: 2px;
        }

        .mention-path {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Mention highlights in messages */
        .mention-highlight {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .status {
            font-style: italic;
            opacity: 0.7;
            padding: 8px 12px;
            text-align: center;
        }

        /* Enhanced Week 2 Features */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 12px;
            max-width: 85%;
            align-self: flex-start;
            animation: fadeIn 0.3s ease-in;
            margin-bottom: 12px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background-color: var(--vscode-foreground);
            border-radius: 50%;
            opacity: 0.4;
            animation: typingDot 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes typingDot {
            0%, 80%, 100% {
                opacity: 0.4;
                transform: scale(1);
            }
            40% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.error {
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            color: var(--vscode-inputValidation-errorForeground);
        }

        .message-timestamp {
            font-size: 11px;
            opacity: 0.6;
            margin-top: 4px;
            text-align: right;
        }

        .assistant-message .message-timestamp {
            text-align: left;
        }

        /* Code block styling */
        .code-block {
            background-color: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 6px;
            margin: 8px 0;
            overflow: hidden;
        }

        .code-header {
            background-color: var(--vscode-sideBar-background);
            padding: 8px 12px;
            border-bottom: 1px solid var(--vscode-input-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .code-content {
            padding: 12px;
            font-family: var(--vscode-editor-font-family);
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
        }

        .copy-button {
            background: none;
            border: none;
            color: var(--vscode-foreground);
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            transition: background-color 0.2s ease;
        }

        .copy-button:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        /* Enhanced animations */
        .message {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Augment-style header -->
    <div class="header">
        <div class="header-left">
            <div class="header-logo">🚀</div>
            <div>
                <h1 class="header-title">UIOrbit</h1>
                <p class="header-subtitle">AI Frontend Development Assistant</p>
            </div>
        </div>
        <div class="header-right">
            <div class="usage-indicator" id="usageIndicator" onclick="showUsageDetails()" title="Click to view usage details">
                <span id="usageText">--/100</span>
                <div class="usage-bar">
                    <div class="usage-fill" id="usageFill" style="width: 0%"></div>
                </div>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <div class="status-dot"></div>
                <span id="status">Ready</span>
            </div>
        </div>
    </div>

    <!-- Enhanced chat container -->
    <div class="chat-container" id="chatContainer">
        <!-- Messages will be added here dynamically -->
    </div>

    <!-- Augment-style input area -->
    <div class="input-container">
        <button id="attachmentButton" class="attachment-button" title="Attach files (images, code)">
            📎
        </button>
        <input type="file" id="attachmentInput" class="attachment-input"
               accept=".png,.jpg,.jpeg,.gif,.webp,.svg,.js,.ts,.jsx,.tsx,.css,.html,.json,.md,.txt"
               multiple>
        <div class="input-wrapper">
            <div id="attachmentPreview" class="attachment-preview" style="display: none;"></div>
            <textarea
                id="messageInput"
                class="message-input"
                placeholder="Ask me anything about frontend development... (Ctrl+Enter to send)"
                rows="1"
            ></textarea>
        </div>
        <button id="sendButton" class="send-button">
            <span class="send-icon">➤</span>
            <span>Send</span>
        </button>
    </div>

    <script nonce="${nonce}">
        console.log('UIOrbit simple webview loaded');

        const vscode = acquireVsCodeApi();
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const attachmentButton = document.getElementById('attachmentButton');
        const attachmentInput = document.getElementById('attachmentInput');
        const attachmentPreview = document.getElementById('attachmentPreview');
        const status = document.getElementById('status');
        let isTyping = false;
        let attachedFiles = [];

        // Send ready message
        vscode.postMessage({ type: 'webview-loaded' });
        vscode.postMessage({ type: 'ready' });

        // Request initial usage stats
        vscode.postMessage({ type: 'get-usage-stats' });

        // Handle send button click
        sendButton.addEventListener('click', sendMessage);

        // Handle keyboard shortcuts
        messageInput.addEventListener('keydown', (e) => {
            // Ctrl+Enter or Cmd+Enter to send
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
            // Shift+Enter for new line (default behavior)
            // Enter alone for new line (changed from send to be more user-friendly)
        });

        // Auto-resize textarea
        messageInput.addEventListener('input', (e) => {
            messageInput.style.height = 'auto';
            messageInput.style.height = messageInput.scrollHeight + 'px';

            // Handle @ mentions
            handleAtMentions(e);
        });

        // Handle @ mentions functionality
        let mentionSuggestions = [];
        let showingMentions = false;
        let mentionDropdown = null;

        function handleAtMentions(e) {
            const text = messageInput.value;
            const cursorPos = messageInput.selectionStart;

            // Find @ symbol before cursor
            const beforeCursor = text.substring(0, cursorPos);
            const atMatch = beforeCursor.match(/@([^@\s]*)$/);

            if (atMatch) {
                const query = atMatch[1];
                showMentionSuggestions(query, cursorPos - atMatch[0].length);
            } else {
                hideMentionSuggestions();
            }
        }

        function showMentionSuggestions(query, atPosition) {
            // Request suggestions from extension
            vscode.postMessage({
                type: 'get-mention-suggestions',
                query: query
            });

            showingMentions = true;
        }

        function hideMentionSuggestions() {
            if (mentionDropdown) {
                mentionDropdown.remove();
                mentionDropdown = null;
            }
            showingMentions = false;
        }

        function createMentionDropdown(suggestions, atPosition) {
            hideMentionSuggestions();

            if (suggestions.length === 0) return;

            mentionDropdown = document.createElement('div');
            mentionDropdown.className = 'mention-dropdown';

            suggestions.forEach((suggestion, index) => {
                const item = document.createElement('div');
                item.className = 'mention-item';
                item.innerHTML = \`
                    <span class="mention-icon">\${getMentionIcon(suggestion.type)}</span>
                    <div class="mention-info">
                        <div class="mention-name">\${suggestion.name}</div>
                        <div class="mention-path">\${suggestion.path}</div>
                    </div>
                \`;

                item.addEventListener('click', () => {
                    insertMention(suggestion, atPosition);
                });

                mentionDropdown.appendChild(item);
            });

            // Position dropdown
            const inputRect = messageInput.getBoundingClientRect();
            mentionDropdown.style.position = 'absolute';
            mentionDropdown.style.bottom = '100%';
            mentionDropdown.style.left = '0';
            mentionDropdown.style.marginBottom = '8px';

            messageInput.parentElement.style.position = 'relative';
            messageInput.parentElement.appendChild(mentionDropdown);
        }

        function getMentionIcon(type) {
            switch (type) {
                case 'file': return '📄';
                case 'component': return '🧩';
                case 'function': return '⚡';
                case 'class': return '🏗️';
                case 'variable': return '📦';
                case 'type': return '🏷️';
                default: return '📄';
            }
        }

        function insertMention(suggestion, atPosition) {
            const text = messageInput.value;
            const beforeAt = text.substring(0, atPosition);
            const afterCursor = text.substring(messageInput.selectionStart);

            const mentionText = \`@\${suggestion.name}\`;
            const newText = beforeAt + mentionText + afterCursor;

            messageInput.value = newText;
            messageInput.focus();

            // Set cursor after mention
            const newCursorPos = atPosition + mentionText.length;
            messageInput.setSelectionRange(newCursorPos, newCursorPos);

            hideMentionSuggestions();
        }

        // Handle keyboard navigation in mentions
        messageInput.addEventListener('keydown', (e) => {
            if (showingMentions && mentionDropdown) {
                const items = mentionDropdown.querySelectorAll('.mention-item');
                const selected = mentionDropdown.querySelector('.mention-item.selected');
                let selectedIndex = selected ? Array.from(items).indexOf(selected) : -1;

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                    updateMentionSelection(items, selectedIndex);
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, 0);
                    updateMentionSelection(items, selectedIndex);
                } else if (e.key === 'Enter' && selectedIndex >= 0) {
                    e.preventDefault();
                    items[selectedIndex].click();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    hideMentionSuggestions();
                }
            }

            // Original keyboard shortcuts
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        });

        function updateMentionSelection(items, selectedIndex) {
            items.forEach((item, index) => {
                item.classList.toggle('selected', index === selectedIndex);
            });
        }

        // Handle attachment button click
        attachmentButton.addEventListener('click', () => {
            attachmentInput.click();
        });

        // Handle file selection
        attachmentInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            files.forEach(file => {
                if (attachedFiles.length < 5) { // Limit to 5 files
                    attachedFiles.push(file);
                }
            });
            updateAttachmentPreview();
            e.target.value = ''; // Reset input
        });

        function updateAttachmentPreview() {
            if (attachedFiles.length === 0) {
                attachmentPreview.style.display = 'none';
                return;
            }

            attachmentPreview.style.display = 'flex';
            attachmentPreview.innerHTML = '';

            attachedFiles.forEach((file, index) => {
                const item = document.createElement('div');
                item.className = 'attachment-item';

                const icon = getFileIcon(file.type, file.name);
                const name = file.name.length > 20 ? file.name.substring(0, 17) + '...' : file.name;

                item.innerHTML = \`
                    <span class="file-icon">\${icon}</span>
                    <span class="file-name" title="\${file.name}">\${name}</span>
                    <button class="remove-btn" onclick="removeAttachment(\${index})">×</button>
                \`;

                attachmentPreview.appendChild(item);
            });
        }

        function getFileIcon(type, name) {
            if (type.startsWith('image/')) return '🖼️';
            if (name.endsWith('.js') || name.endsWith('.jsx')) return '📄';
            if (name.endsWith('.ts') || name.endsWith('.tsx')) return '📘';
            if (name.endsWith('.css') || name.endsWith('.scss')) return '🎨';
            if (name.endsWith('.html')) return '🌐';
            if (name.endsWith('.json')) return '📋';
            if (name.endsWith('.md')) return '📝';
            return '📄';
        }

        function removeAttachment(index) {
            attachedFiles.splice(index, 1);
            updateAttachmentPreview();
        }

        // Make removeAttachment globally accessible
        window.removeAttachment = removeAttachment;

        function sendMessage() {
            console.log('Send button clicked!');
            const text = messageInput.value.trim();
            console.log('Message text:', text);

            if ((!text && attachedFiles.length === 0) || isTyping) {
                console.log('Message empty or typing in progress');
                return;
            }

            // Process attachments
            const attachments = [];
            if (attachedFiles.length > 0) {
                attachedFiles.forEach(file => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const attachment = {
                            name: file.name,
                            type: file.type,
                            size: file.size,
                            data: e.target.result
                        };

                        if (file.type.startsWith('image/')) {
                            attachment.data = e.target.result; // Base64 data URL
                        } else {
                            attachment.content = e.target.result; // Text content
                        }

                        attachments.push(attachment);

                        // Send when all files are processed
                        if (attachments.length === attachedFiles.length) {
                            sendMessageWithAttachments(text, attachments);
                        }
                    };

                    if (file.type.startsWith('image/')) {
                        reader.readAsDataURL(file);
                    } else {
                        reader.readAsText(file);
                    }
                });
            } else {
                sendMessageWithAttachments(text, []);
            }
        }

        function sendMessageWithAttachments(text, attachments) {
            // Add user message to chat (with attachments if any)
            addMessage(text, 'user', false, attachments);

            // Send to extension
            console.log('Sending message to extension:', text, attachments);
            vscode.postMessage({
                type: 'chat-message',
                text: text,
                attachments: attachments
            });

            // Clear input and attachments
            messageInput.value = '';
            messageInput.style.height = 'auto';
            attachedFiles = [];
            updateAttachmentPreview();

            // Show typing status
            setTyping(true);
        }

        function addMessage(text, sender, isError = false, attachments = []) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + sender + '-message' + (isError ? ' error' : '');

            // Add attachments if any
            if (attachments && attachments.length > 0) {
                const attachmentsDiv = document.createElement('div');
                attachmentsDiv.className = 'message-attachments';

                attachments.forEach(attachment => {
                    const attachmentDiv = document.createElement('div');
                    attachmentDiv.className = 'attachment-display';

                    if (attachment.type && attachment.type.startsWith('image/')) {
                        attachmentDiv.innerHTML = \`
                            <img src="\${attachment.data}" alt="\${attachment.name}" style="max-width: 200px; max-height: 200px; border-radius: 8px;">
                            <div class="attachment-name">\${attachment.name}</div>
                        \`;
                    } else {
                        attachmentDiv.innerHTML = \`
                            <div class="code-attachment">
                                <div class="attachment-header">
                                    <span class="file-icon">\${getFileIcon(attachment.type, attachment.name)}</span>
                                    <span class="attachment-name">\${attachment.name}</span>
                                </div>
                                <pre class="attachment-code">\${attachment.content ? attachment.content.substring(0, 200) + (attachment.content.length > 200 ? '...' : '') : ''}</pre>
                            </div>
                        \`;
                    }

                    attachmentsDiv.appendChild(attachmentDiv);
                });

                messageDiv.appendChild(attachmentsDiv);
            }

            // Create message content with timestamp
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // Process text for code blocks and formatting
            const processedContent = processMessageContent(text);
            messageContent.innerHTML = processedContent;

            // Add timestamp
            const timestamp = document.createElement('div');
            timestamp.className = 'message-timestamp';
            timestamp.textContent = new Date().toLocaleTimeString();

            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(timestamp);

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function processMessageContent(text) {
            // Process code blocks
            let processed = text.replace(/\`\`\`(\\w+)?\\n([\\s\\S]*?)\`\`\`/g, function(match, language, code) {
                const lang = language || 'text';
                return '<div class="code-block">' +
                    '<div class="code-header">' +
                    '<span>' + lang + '</span>' +
                    '<button class="copy-button" onclick="copyCode(this)">Copy</button>' +
                    '</div>' +
                    '<div class="code-content"><pre><code>' + escapeHtml(code.trim()) + '</code></pre></div>' +
                    '</div>';
            });

            // Process inline code
            processed = processed.replace(/\`([^\`]+)\`/g, '<code style="background-color: var(--vscode-textCodeBlock-background); padding: 2px 4px; border-radius: 3px;">$1</code>');

            // Process @ mentions
            processed = processed.replace(/@([a-zA-Z0-9_-]+)/g, '<span class="mention-highlight">@$1</span>');

            // Process line breaks
            processed = processed.replace(/\\n/g, '<br>');

            return processed;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyCode(button) {
            const codeContent = button.closest('.code-block').querySelector('code').textContent;
            navigator.clipboard.writeText(codeContent).then(() => {
                button.textContent = 'Copied!';
                setTimeout(() => {
                    button.textContent = 'Copy';
                }, 2000);
            });
        }

        function setTyping(typing) {
            isTyping = typing;
            sendButton.disabled = typing;

            // Update status with enhanced styling
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('status');

            if (typing) {
                statusText.textContent = 'Thinking...';
                statusIndicator.style.backgroundColor = 'var(--uiorbit-warning)';
                statusIndicator.style.color = 'white';
                sendButton.innerHTML = '<span class="send-icon">⏳</span><span>Sending...</span>';
            } else {
                statusText.textContent = 'Ready';
                statusIndicator.style.backgroundColor = 'var(--vscode-badge-background)';
                statusIndicator.style.color = 'var(--vscode-badge-foreground)';
                sendButton.innerHTML = '<span class="send-icon">➤</span><span>Send</span>';
            }

            // Show/hide typing indicator
            const existingIndicator = document.querySelector('.typing-indicator');
            if (typing && !existingIndicator) {
                showTypingIndicator();
            } else if (!typing && existingIndicator) {
                hideTypingIndicator();
            }
        }

        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-indicator';
            typingDiv.innerHTML =
                '<span>UIOrbit is thinking</span>' +
                '<div class="typing-dots">' +
                    '<div class="typing-dot"></div>' +
                    '<div class="typing-dot"></div>' +
                    '<div class="typing-dot"></div>' +
                '</div>';

            chatContainer.appendChild(typingDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const indicator = document.querySelector('.typing-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        function showUsageDetails() {
            vscode.postMessage({ type: 'show-usage-details' });
        }

        function updateUsageDisplay(stats) {
            const usageText = document.getElementById('usageText');
            const usageFill = document.getElementById('usageFill');
            const usageIndicator = document.getElementById('usageIndicator');

            if (usageText && usageFill && usageIndicator) {
                usageText.textContent = stats.current + '/' + stats.limit;
                usageFill.style.width = stats.percentage + '%';

                // Update colors based on usage
                if (stats.percentage >= 90) {
                    usageIndicator.style.borderColor = 'var(--uiorbit-error)';
                    usageIndicator.style.color = 'var(--uiorbit-error)';
                } else if (stats.percentage >= 70) {
                    usageIndicator.style.borderColor = 'var(--uiorbit-warning)';
                    usageIndicator.style.color = 'var(--uiorbit-warning)';
                } else {
                    usageIndicator.style.borderColor = 'var(--vscode-input-border)';
                    usageIndicator.style.color = 'var(--vscode-foreground)';
                }

                // Update tooltip
                usageIndicator.title = 'Usage: ' + stats.current + '/' + stats.limit + ' requests this month. Resets: ' + new Date(stats.resetDate).toLocaleDateString();
            }
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            console.log('Received message:', message);

            switch (message.type) {
                case 'assistant-message':
                    addMessage(message.text, 'assistant', message.isError);
                    setTyping(false);
                    break;
                case 'assistant-typing':
                    setTyping(message.isTyping);
                    break;
                case 'mention-suggestions':
                    if (showingMentions) {
                        createMentionDropdown(message.suggestions, 0);
                    }
                    break;
                case 'welcome':
                    status.textContent = 'Connected and ready!';
                    break;
                case 'usage-stats':
                    updateUsageDisplay(message.stats);
                    break;
                default:
                    console.log('Unknown message type:', message.type);
            }
        });

        console.log('UIOrbit webview initialized successfully');
    </script>
</body>
</html>`;
  }

  /**
   * Generate a nonce for CSP
   */
  private getNonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }
}
