<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIOrbit Settings</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            padding: 20px;
        }

        .settings-header {
            margin-bottom: 32px;
        }

        .settings-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--vscode-foreground);
            margin-bottom: 8px;
        }

        .settings-subtitle {
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
        }

        .settings-section {
            margin-bottom: 32px;
            padding: 20px;
            background: var(--vscode-sideBar-background);
            border-radius: 8px;
            border: 1px solid var(--vscode-panel-border);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--vscode-foreground);
        }

        .setting-item {
            margin-bottom: 20px;
        }

        .setting-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--vscode-foreground);
        }

        .setting-description {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 8px;
        }

        .setting-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-size: 14px;
        }

        .setting-input:focus {
            outline: none;
            border-color: var(--vscode-focusBorder);
        }

        .setting-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            background: var(--vscode-dropdown-background);
            color: var(--vscode-dropdown-foreground);
            font-size: 14px;
        }

        .setting-checkbox {
            margin-right: 8px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .api-key-input {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background: #4CAF50;
        }

        .status-disconnected {
            background: #F44336;
        }

        .status-warning {
            background: #FF9800;
        }

        .save-button {
            padding: 10px 20px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .save-button:hover {
            background: var(--vscode-button-hoverBackground);
        }

        .reset-button {
            padding: 10px 20px;
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-left: 8px;
        }

        .reset-button:hover {
            background: var(--vscode-button-secondaryHoverBackground);
        }

        .info-box {
            padding: 12px;
            background: var(--vscode-textCodeBlock-background);
            border-left: 4px solid var(--vscode-button-background);
            border-radius: 4px;
            margin-top: 8px;
        }

        .info-box-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .info-box-text {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }

        .usage-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .usage-stat {
            padding: 16px;
            background: var(--vscode-input-background);
            border-radius: 6px;
            text-align: center;
        }

        .usage-stat-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--vscode-button-background);
        }

        .usage-stat-label {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-top: 4px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--vscode-progressBar-background);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background: var(--vscode-button-background);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="settings-header">
        <div class="settings-title">⚙️ UIOrbit Settings</div>
        <div class="settings-subtitle">Configure your AI-powered frontend development environment</div>
    </div>

    <!-- AI Configuration -->
    <div class="settings-section">
        <div class="section-title">🤖 AI Configuration</div>
        
        <div class="setting-item">
            <label class="setting-label" for="apiKey">
                <span class="status-indicator" id="apiStatus"></span>
                OpenAI API Key
            </label>
            <div class="setting-description">
                Your OpenAI API key for AI-powered code generation and assistance
            </div>
            <input type="password" id="apiKey" class="setting-input api-key-input" placeholder="sk-...">
            <div class="info-box">
                <div class="info-box-title">🔒 Privacy First</div>
                <div class="info-box-text">
                    Your API key is stored locally and never shared. Only user prompts and small code snippets are sent to OpenAI for generation.
                </div>
            </div>
        </div>

        <div class="setting-item">
            <label class="setting-label" for="model">AI Model</label>
            <div class="setting-description">Choose the AI model for code generation</div>
            <select id="model" class="setting-select">
                <option value="gpt-4">GPT-4 (Recommended)</option>
                <option value="gpt-4-turbo">GPT-4 Turbo</option>
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo (Faster)</option>
            </select>
        </div>
    </div>

    <!-- Project Defaults -->
    <div class="settings-section">
        <div class="section-title">🎨 Project Defaults</div>
        
        <div class="setting-item">
            <label class="setting-label" for="framework">Default Framework</label>
            <div class="setting-description">Framework to use when creating new projects</div>
            <select id="framework" class="setting-select">
                <option value="react">React</option>
                <option value="vue">Vue.js</option>
                <option value="angular">Angular</option>
                <option value="svelte">Svelte</option>
            </select>
        </div>

        <div class="setting-item">
            <label class="setting-label" for="styling">Default Styling</label>
            <div class="setting-description">CSS framework/approach for new components</div>
            <select id="styling" class="setting-select">
                <option value="tailwind">Tailwind CSS</option>
                <option value="css">Plain CSS</option>
                <option value="scss">SCSS</option>
                <option value="styled-components">Styled Components</option>
                <option value="emotion">Emotion</option>
            </select>
        </div>

        <div class="setting-item">
            <label class="setting-label" for="packageManager">Package Manager</label>
            <div class="setting-description">Package manager for new projects</div>
            <select id="packageManager" class="setting-select">
                <option value="npm">npm</option>
                <option value="yarn">Yarn</option>
                <option value="pnpm">pnpm</option>
            </select>
        </div>
    </div>

    <!-- Features -->
    <div class="settings-section">
        <div class="section-title">✨ Features</div>
        
        <div class="setting-item">
            <label class="checkbox-label">
                <input type="checkbox" id="accessibility" class="setting-checkbox">
                <span class="setting-label">Enable Accessibility Features</span>
            </label>
            <div class="setting-description">Include ARIA attributes and accessibility best practices in generated code</div>
        </div>

        <div class="setting-item">
            <label class="checkbox-label">
                <input type="checkbox" id="responsive" class="setting-checkbox">
                <span class="setting-label">Enable Responsive Design</span>
            </label>
            <div class="setting-description">Generate responsive components with mobile-first approach</div>
        </div>

        <div class="setting-item">
            <label class="checkbox-label">
                <input type="checkbox" id="typescript" class="setting-checkbox">
                <span class="setting-label">Use TypeScript by Default</span>
            </label>
            <div class="setting-description">Generate TypeScript code with proper type definitions</div>
        </div>

        <div class="setting-item">
            <label class="checkbox-label">
                <input type="checkbox" id="autoIndex" class="setting-checkbox">
                <span class="setting-label">Auto-Index on File Changes</span>
            </label>
            <div class="setting-description">Automatically re-index codebase when files are modified</div>
        </div>
    </div>

    <!-- Usage Statistics -->
    <div class="settings-section">
        <div class="section-title">📊 Usage Statistics</div>
        <div class="usage-stats">
            <div class="usage-stat">
                <div class="usage-stat-value" id="requestsUsed">0</div>
                <div class="usage-stat-label">Requests This Month</div>
            </div>
            <div class="usage-stat">
                <div class="usage-stat-value" id="componentsGenerated">0</div>
                <div class="usage-stat-label">Components Generated</div>
            </div>
            <div class="usage-stat">
                <div class="usage-stat-value" id="projectsCreated">0</div>
                <div class="usage-stat-label">Projects Created</div>
            </div>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" id="usageProgress" style="width: 0%"></div>
        </div>
        <div class="setting-description" style="margin-top: 8px;">
            Community plan: <span id="usageText">0/100 requests used</span>
        </div>
    </div>

    <!-- Actions -->
    <div class="settings-section">
        <button class="save-button" onclick="saveSettings()">Save Settings</button>
        <button class="reset-button" onclick="resetSettings()">Reset to Defaults</button>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        // Load settings on page load
        window.addEventListener('load', () => {
            vscode.postMessage({ type: 'loadSettings' });
        });

        // Listen for messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.type) {
                case 'settingsLoaded':
                    populateSettings(message.settings);
                    break;
                case 'usageStats':
                    updateUsageStats(message.stats);
                    break;
                case 'apiStatus':
                    updateApiStatus(message.status);
                    break;
            }
        });

        function populateSettings(settings) {
            document.getElementById('apiKey').value = settings.apiKey || '';
            document.getElementById('model').value = settings.model || 'gpt-4';
            document.getElementById('framework').value = settings.framework || 'react';
            document.getElementById('styling').value = settings.styling || 'tailwind';
            document.getElementById('packageManager').value = settings.packageManager || 'npm';
            document.getElementById('accessibility').checked = settings.accessibility !== false;
            document.getElementById('responsive').checked = settings.responsive !== false;
            document.getElementById('typescript').checked = settings.typescript !== false;
            document.getElementById('autoIndex').checked = settings.autoIndex !== false;
        }

        function saveSettings() {
            const settings = {
                apiKey: document.getElementById('apiKey').value,
                model: document.getElementById('model').value,
                framework: document.getElementById('framework').value,
                styling: document.getElementById('styling').value,
                packageManager: document.getElementById('packageManager').value,
                accessibility: document.getElementById('accessibility').checked,
                responsive: document.getElementById('responsive').checked,
                typescript: document.getElementById('typescript').checked,
                autoIndex: document.getElementById('autoIndex').checked
            };

            vscode.postMessage({ type: 'saveSettings', settings });
        }

        function resetSettings() {
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                vscode.postMessage({ type: 'resetSettings' });
            }
        }

        function updateUsageStats(stats) {
            document.getElementById('requestsUsed').textContent = stats.requestsUsed || 0;
            document.getElementById('componentsGenerated').textContent = stats.componentsGenerated || 0;
            document.getElementById('projectsCreated').textContent = stats.projectsCreated || 0;
            
            const percentage = Math.min((stats.requestsUsed / stats.requestsLimit) * 100, 100);
            document.getElementById('usageProgress').style.width = percentage + '%';
            document.getElementById('usageText').textContent = `${stats.requestsUsed}/${stats.requestsLimit} requests used`;
        }

        function updateApiStatus(status) {
            const indicator = document.getElementById('apiStatus');
            indicator.className = 'status-indicator';
            
            switch (status) {
                case 'connected':
                    indicator.classList.add('status-connected');
                    break;
                case 'error':
                    indicator.classList.add('status-disconnected');
                    break;
                default:
                    indicator.classList.add('status-warning');
            }
        }

        // Auto-save on input changes
        document.addEventListener('change', () => {
            setTimeout(saveSettings, 500);
        });
    </script>
</body>
</html>
