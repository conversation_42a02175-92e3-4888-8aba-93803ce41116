import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';
import { ASTAnalysisService } from './ASTAnalysisService';
import { VectorDatabaseService } from './VectorDatabaseService';
import { ContextEngineService } from './ContextEngineService';

export interface FileChangeEvent {
  type: 'created' | 'modified' | 'deleted' | 'renamed';
  filePath: string;
  oldPath?: string; // For rename events
  timestamp: number;
  size?: number;
}

export interface WatchingStats {
  totalFiles: number;
  watchedFiles: number;
  pendingUpdates: number;
  lastUpdate: number;
  processingQueue: number;
}

/**
 * File Watching Service - Real-time codebase monitoring and incremental updates
 * This service watches for file changes and updates the AST analysis and vector database
 * in real-time to maintain accurate codebase understanding.
 */
export class FileWatchingService {
  private astAnalysisService: ASTAnalysisService;
  private vectorDatabaseService: VectorDatabaseService;
  private contextEngineService: ContextEngineService;
  
  private fileWatcher?: vscode.FileSystemWatcher;
  private processingQueue: Map<string, FileChangeEvent> = new Map();
  private isProcessing: boolean = false;
  private batchTimer?: NodeJS.Timeout;
  private readonly BATCH_DELAY = 1000; // 1 second delay for batching
  private readonly SUPPORTED_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx', '.css', '.scss', '.html', '.json'];
  private readonly MAX_QUEUE_SIZE = 100; // Prevent memory issues
  private readonly DEBOUNCE_DELAY = 300; // Debounce rapid file changes
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private errorCount: number = 0;
  private readonly MAX_ERRORS = 10; // Stop processing after too many errors
  
  private stats: WatchingStats = {
    totalFiles: 0,
    watchedFiles: 0,
    pendingUpdates: 0,
    lastUpdate: 0,
    processingQueue: 0
  };

  constructor(
    astAnalysisService: ASTAnalysisService,
    vectorDatabaseService: VectorDatabaseService,
    contextEngineService: ContextEngineService
  ) {
    this.astAnalysisService = astAnalysisService;
    this.vectorDatabaseService = vectorDatabaseService;
    this.contextEngineService = contextEngineService;
    Logger.info('File Watching Service initialized');
  }

  /**
   * Start watching workspace files
   */
  async startWatching(): Promise<void> {
    try {
      Logger.info('Starting file watching...');

      // Create file system watcher for supported file types
      const pattern = `**/*.{${this.SUPPORTED_EXTENSIONS.map(ext => ext.slice(1)).join(',')}}`;
      this.fileWatcher = vscode.workspace.createFileSystemWatcher(pattern);

      // Set up event handlers
      this.fileWatcher.onDidCreate(this.handleFileCreated.bind(this));
      this.fileWatcher.onDidChange(this.handleFileChanged.bind(this));
      this.fileWatcher.onDidDelete(this.handleFileDeleted.bind(this));

      // Also watch for document changes in open editors
      vscode.workspace.onDidChangeTextDocument(this.handleDocumentChanged.bind(this));
      vscode.workspace.onDidSaveTextDocument(this.handleDocumentSaved.bind(this));

      // Get initial file count
      await this.updateFileStats();

      Logger.info(`File watching started. Monitoring ${this.stats.totalFiles} files.`);

    } catch (error) {
      Logger.error('Error starting file watching:', error);
    }
  }

  /**
   * Stop watching files
   */
  stopWatching(): void {
    if (this.fileWatcher) {
      this.fileWatcher.dispose();
      this.fileWatcher = undefined;
    }

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
    }

    Logger.info('File watching stopped');
  }

  /**
   * Handle file creation
   */
  private async handleFileCreated(uri: vscode.Uri): Promise<void> {
    const filePath = uri.fsPath;
    
    if (!this.isSupportedFile(filePath)) {
      return;
    }

    Logger.debug(`File created: ${filePath}`);

    const event: FileChangeEvent = {
      type: 'created',
      filePath,
      timestamp: Date.now()
    };

    this.queueForProcessing(filePath, event);
  }

  /**
   * Handle file modification
   */
  private async handleFileChanged(uri: vscode.Uri): Promise<void> {
    const filePath = uri.fsPath;
    
    if (!this.isSupportedFile(filePath)) {
      return;
    }

    Logger.debug(`File changed: ${filePath}`);

    try {
      const stat = await vscode.workspace.fs.stat(uri);
      const event: FileChangeEvent = {
        type: 'modified',
        filePath,
        timestamp: Date.now(),
        size: stat.size
      };

      this.queueForProcessing(filePath, event);

    } catch (error) {
      Logger.warn(`Error getting file stats for ${filePath}:`, error);
    }
  }

  /**
   * Handle file deletion
   */
  private async handleFileDeleted(uri: vscode.Uri): Promise<void> {
    const filePath = uri.fsPath;
    
    if (!this.isSupportedFile(filePath)) {
      return;
    }

    Logger.debug(`File deleted: ${filePath}`);

    const event: FileChangeEvent = {
      type: 'deleted',
      filePath,
      timestamp: Date.now()
    };

    // Process deletion immediately (no need to batch)
    await this.processFileChange(event);
  }

  /**
   * Handle document changes in open editors
   */
  private handleDocumentChanged(event: vscode.TextDocumentChangeEvent): void {
    const filePath = event.document.uri.fsPath;
    
    if (!this.isSupportedFile(filePath) || event.document.isDirty) {
      return; // Wait for save
    }

    Logger.debug(`Document changed: ${filePath}`);

    const changeEvent: FileChangeEvent = {
      type: 'modified',
      filePath,
      timestamp: Date.now()
    };

    this.queueForProcessing(filePath, changeEvent);
  }

  /**
   * Handle document save
   */
  private async handleDocumentSaved(document: vscode.TextDocument): Promise<void> {
    const filePath = document.uri.fsPath;
    
    if (!this.isSupportedFile(filePath)) {
      return;
    }

    Logger.debug(`Document saved: ${filePath}`);

    const event: FileChangeEvent = {
      type: 'modified',
      filePath,
      timestamp: Date.now(),
      size: Buffer.byteLength(document.getText(), 'utf8')
    };

    // Process saves with higher priority
    await this.processFileChange(event);
  }

  /**
   * Queue file change for batch processing with debouncing and queue management
   */
  private queueForProcessing(filePath: string, event: FileChangeEvent): void {
    // Check queue size limit
    if (this.processingQueue.size >= this.MAX_QUEUE_SIZE) {
      Logger.warn(`File watching queue is full (${this.MAX_QUEUE_SIZE}), dropping oldest events`);
      // Remove oldest entries
      const entries = Array.from(this.processingQueue.entries());
      entries.slice(0, 10).forEach(([key]) => this.processingQueue.delete(key));
    }

    // Debounce rapid changes to the same file
    const existingTimer = this.debounceTimers.get(filePath);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set debounce timer for this file
    const debounceTimer = setTimeout(() => {
      // Replace any existing event for this file (keep only the latest)
      this.processingQueue.set(filePath, event);
      this.stats.pendingUpdates = this.processingQueue.size;
      this.debounceTimers.delete(filePath);

      // Reset batch timer
      if (this.batchTimer) {
        clearTimeout(this.batchTimer);
      }

      this.batchTimer = setTimeout(() => {
        this.processBatch();
      }, this.BATCH_DELAY);
    }, this.DEBOUNCE_DELAY);

    this.debounceTimers.set(filePath, debounceTimer);
  }

  /**
   * Process batch of file changes
   */
  private async processBatch(): Promise<void> {
    if (this.isProcessing || this.processingQueue.size === 0) {
      return;
    }

    this.isProcessing = true;
    this.stats.processingQueue = this.processingQueue.size;

    try {
      Logger.info(`Processing batch of ${this.processingQueue.size} file changes`);

      const events = Array.from(this.processingQueue.values());
      this.processingQueue.clear();
      this.stats.pendingUpdates = 0;

      // Process events in parallel (with concurrency limit)
      const concurrencyLimit = 5;
      for (let i = 0; i < events.length; i += concurrencyLimit) {
        const batch = events.slice(i, i + concurrencyLimit);
        await Promise.allSettled(
          batch.map(event => this.processFileChange(event))
        );
      }

      this.stats.lastUpdate = Date.now();
      Logger.info('Batch processing completed');

    } catch (error) {
      Logger.error('Error processing batch:', error);
    } finally {
      this.isProcessing = false;
      this.stats.processingQueue = 0;
    }
  }

  /**
   * Process individual file change
   */
  private async processFileChange(event: FileChangeEvent): Promise<void> {
    try {
      Logger.debug(`Processing ${event.type} for ${event.filePath}`);

      switch (event.type) {
        case 'created':
        case 'modified':
          await this.handleFileUpdate(event.filePath);
          break;
          
        case 'deleted':
          await this.handleFileRemoval(event.filePath);
          break;
          
        case 'renamed':
          if (event.oldPath) {
            await this.handleFileRemoval(event.oldPath);
            await this.handleFileUpdate(event.filePath);
          }
          break;
      }

    } catch (error) {
      Logger.error(`Error processing file change for ${event.filePath}:`, error);
    }
  }

  /**
   * Handle file update (creation or modification)
   */
  private async handleFileUpdate(filePath: string): Promise<void> {
    try {
      // Re-analyze the file
      await this.astAnalysisService.analyzeFile(filePath);
      
      // Update vector database
      await this.vectorDatabaseService.indexFile(filePath);
      
      // Clear context cache for this file
      this.contextEngineService.clearCache();
      
      Logger.debug(`Updated analysis for ${filePath}`);

    } catch (error) {
      Logger.error(`Error updating file ${filePath}:`, error);
    }
  }

  /**
   * Handle file removal
   */
  private async handleFileRemoval(filePath: string): Promise<void> {
    try {
      // Remove from vector database (if it has such functionality)
      // For now, we'll just clear the cache
      this.contextEngineService.clearCache();
      
      Logger.debug(`Removed analysis for ${filePath}`);

    } catch (error) {
      Logger.error(`Error removing file ${filePath}:`, error);
    }
  }

  /**
   * Check if file is supported for watching
   */
  private isSupportedFile(filePath: string): boolean {
    // Skip node_modules and other ignored directories
    if (filePath.includes('node_modules') || 
        filePath.includes('.git') || 
        filePath.includes('dist') || 
        filePath.includes('build')) {
      return false;
    }

    // Check file extension
    const ext = filePath.substring(filePath.lastIndexOf('.'));
    return this.SUPPORTED_EXTENSIONS.includes(ext);
  }

  /**
   * Update file statistics
   */
  private async updateFileStats(): Promise<void> {
    try {
      if (!vscode.workspace.workspaceFolders) {
        return;
      }

      let totalFiles = 0;
      let watchedFiles = 0;

      for (const folder of vscode.workspace.workspaceFolders) {
        const pattern = new vscode.RelativePattern(folder, '**/*');
        const files = await vscode.workspace.findFiles(pattern, '**/node_modules/**');
        
        totalFiles += files.length;
        watchedFiles += files.filter(uri => this.isSupportedFile(uri.fsPath)).length;
      }

      this.stats.totalFiles = totalFiles;
      this.stats.watchedFiles = watchedFiles;

    } catch (error) {
      Logger.error('Error updating file stats:', error);
    }
  }

  /**
   * Force re-index of all files
   */
  async reindexWorkspace(): Promise<void> {
    try {
      Logger.info('Starting workspace re-indexing...');

      if (!vscode.workspace.workspaceFolders) {
        Logger.warn('No workspace folders found');
        return;
      }

      const files: string[] = [];
      
      for (const folder of vscode.workspace.workspaceFolders) {
        const pattern = new vscode.RelativePattern(folder, `**/*.{${this.SUPPORTED_EXTENSIONS.map(ext => ext.slice(1)).join(',')}}`);
        const foundFiles = await vscode.workspace.findFiles(pattern, '**/node_modules/**');
        files.push(...foundFiles.map(uri => uri.fsPath));
      }

      Logger.info(`Re-indexing ${files.length} files...`);

      // Process files in batches
      const batchSize = 10;
      for (let i = 0; i < files.length; i += batchSize) {
        const batch = files.slice(i, i + batchSize);
        await Promise.allSettled(
          batch.map(filePath => this.handleFileUpdate(filePath))
        );
        
        Logger.info(`Re-indexed ${Math.min(i + batchSize, files.length)}/${files.length} files`);
      }

      await this.updateFileStats();
      Logger.info('Workspace re-indexing completed');

    } catch (error) {
      Logger.error('Error during workspace re-indexing:', error);
    }
  }

  /**
   * Get watching statistics
   */
  getStats(): WatchingStats {
    return { ...this.stats };
  }

  /**
   * Check if currently processing
   */
  isCurrentlyProcessing(): boolean {
    return this.isProcessing;
  }

  /**
   * Get pending updates count
   */
  getPendingUpdatesCount(): number {
    return this.processingQueue.size;
  }

  /**
   * Force process pending updates immediately
   */
  async flushPendingUpdates(): Promise<void> {
    // Clear all debounce timers
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
    }

    await this.processBatch();
  }

  /**
   * Get real-time file watching statistics
   */
  getWatchingStats(): WatchingStats {
    return { ...this.stats };
  }

  /**
   * Check if file watching is healthy
   */
  isHealthy(): boolean {
    return this.errorCount < this.MAX_ERRORS &&
           this.processingQueue.size < this.MAX_QUEUE_SIZE &&
           this.fileWatcher !== undefined;
  }

  /**
   * Reset error count and resume normal operation
   */
  resetErrorState(): void {
    this.errorCount = 0;
    Logger.info('File watching error state reset');
  }

  /**
   * Pause file watching temporarily
   */
  pause(): void {
    if (this.fileWatcher) {
      this.fileWatcher.dispose();
      this.fileWatcher = undefined;
      Logger.info('File watching paused');
    }
  }

  /**
   * Resume file watching
   */
  async resume(): Promise<void> {
    if (!this.fileWatcher) {
      await this.startWatching();
      Logger.info('File watching resumed');
    }
  }
}
