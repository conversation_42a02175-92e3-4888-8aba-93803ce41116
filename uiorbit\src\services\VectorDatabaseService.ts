import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from '../utils/Logger';
import { ASTAnalysisService, FileAnalysis, ComponentInfo } from './ASTAnalysisService';
import { ProgressReportingService, ProgressStep } from './ProgressReportingService';

export interface CodeEmbedding {
  id: string;
  filePath: string;
  content: string;
  type: 'component' | 'function' | 'class' | 'file' | 'snippet';
  embedding: number[];
  metadata: {
    name?: string;
    language: string;
    framework?: string;
    props?: string[];
    hooks?: string[];
    imports?: string[];
    exports?: string[];
    line: number;
    size: number;
    lastModified: number;
  };
}

export interface SearchResult {
  embedding: CodeEmbedding;
  similarity: number;
  relevance: number;
}

export interface SemanticSearchOptions {
  query: string;
  type?: 'component' | 'function' | 'class' | 'file' | 'snippet';
  language?: string;
  framework?: string;
  limit?: number;
  threshold?: number;
}

export interface VectorDatabaseMetadata {
  version: string;
  createdAt: string;
  lastUpdated: string;
  totalEmbeddings: number;
  workspaceHash: string;
  embeddingModel: string;
}

/**
 * Vector Database Service for semantic code search using embeddings
 */
export class VectorDatabaseService {
  private embeddings: Map<string, CodeEmbedding> = new Map();
  private astAnalysisService: ASTAnalysisService;
  private progressReportingService?: ProgressReportingService;
  private isIndexing: boolean = false;
  private indexingProgress: number = 0;
  private currentProgressSession?: string;
  private dbPath: string;
  private metadataPath: string;
  private metadata: VectorDatabaseMetadata;

  constructor(astAnalysisService: ASTAnalysisService) {
    this.astAnalysisService = astAnalysisService;

    // Initialize database paths
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (workspaceFolder) {
      const dbDir = path.join(workspaceFolder.uri.fsPath, '.uiorbit', 'vectordb');
      this.dbPath = path.join(dbDir, 'embeddings.json');
      this.metadataPath = path.join(dbDir, 'metadata.json');
    } else {
      // Fallback to temp directory
      const dbDir = path.join(require('os').tmpdir(), 'uiorbit-vectordb');
      this.dbPath = path.join(dbDir, 'embeddings.json');
      this.metadataPath = path.join(dbDir, 'metadata.json');
    }

    // Initialize metadata
    this.metadata = {
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      totalEmbeddings: 0,
      workspaceHash: '',
      embeddingModel: 'text-embedding-ada-002'
    };

    Logger.info('Vector Database Service initialized');
    Logger.debug(`Database path: ${this.dbPath}`);
  }

  /**
   * Set progress reporting service (injected after construction)
   */
  setProgressReportingService(service: ProgressReportingService): void {
    this.progressReportingService = service;
  }

  /**
   * Get current indexing status
   */
  getIndexingStatus(): { isIndexing: boolean; progress: number; totalEmbeddings: number } {
    return {
      isIndexing: this.isIndexing,
      progress: this.indexingProgress,
      totalEmbeddings: this.embeddings.size
    };
  }

  /**
   * Initialize the vector database (load existing data if available)
   */
  async initialize(): Promise<void> {
    try {
      Logger.info('Initializing vector database...');

      // Try to load existing database
      await this.loadDatabase();

      // Check if workspace has changed and needs reindexing
      const currentWorkspaceHash = await this.calculateWorkspaceHash();
      if (this.metadata.workspaceHash !== currentWorkspaceHash) {
        Logger.info('Workspace has changed, reindexing required');
        this.metadata.workspaceHash = currentWorkspaceHash;
        // Don't auto-reindex here, let the caller decide
      } else {
        Logger.info(`Loaded existing vector database with ${this.embeddings.size} embeddings`);
      }

    } catch (error) {
      Logger.warn('Failed to load existing database, will create new one:', error);
    }
  }

  /**
   * Load vector database from disk
   */
  private async loadDatabase(): Promise<void> {
    if (await fs.pathExists(this.dbPath) && await fs.pathExists(this.metadataPath)) {
      Logger.debug('Loading vector database from disk...');

      // Load metadata
      const metadataContent = await fs.readJson(this.metadataPath);
      this.metadata = { ...this.metadata, ...metadataContent };

      // Load embeddings
      const embeddingsContent = await fs.readJson(this.dbPath);
      this.embeddings.clear();

      for (const [key, embedding] of Object.entries(embeddingsContent)) {
        this.embeddings.set(key, embedding as CodeEmbedding);
      }

      Logger.info(`Loaded ${this.embeddings.size} embeddings from disk`);
    }
  }

  /**
   * Save vector database to disk
   */
  private async saveDatabase(): Promise<void> {
    try {
      Logger.debug('Saving vector database to disk...');

      // Ensure directory exists
      await fs.ensureDir(path.dirname(this.dbPath));

      // Update metadata
      this.metadata.lastUpdated = new Date().toISOString();
      this.metadata.totalEmbeddings = this.embeddings.size;

      // Save metadata
      await fs.writeJson(this.metadataPath, this.metadata, { spaces: 2 });

      // Convert Map to Object for JSON serialization
      const embeddingsObj = Object.fromEntries(this.embeddings);
      await fs.writeJson(this.dbPath, embeddingsObj, { spaces: 2 });

      Logger.debug(`Saved ${this.embeddings.size} embeddings to disk`);

    } catch (error) {
      Logger.error('Failed to save vector database:', error);
    }
  }

  /**
   * Calculate a hash of the workspace to detect changes
   */
  private async calculateWorkspaceHash(): Promise<string> {
    try {
      const files = await this.getWorkspaceFiles();
      const fileStats = await Promise.all(
        files.map(async (file) => {
          try {
            const stat = await fs.stat(file);
            return `${file}:${stat.mtime.getTime()}:${stat.size}`;
          } catch {
            return `${file}:0:0`;
          }
        })
      );

      // Simple hash based on file paths, modification times, and sizes
      const hashInput = fileStats.sort().join('|');
      return Buffer.from(hashInput).toString('base64').substring(0, 32);

    } catch (error) {
      Logger.warn('Failed to calculate workspace hash:', error);
      return Date.now().toString();
    }
  }

  /**
   * Index the entire workspace for semantic search
   */
  async indexWorkspace(): Promise<void> {
    if (this.isIndexing) {
      Logger.warn('Indexing already in progress');
      return;
    }

    this.isIndexing = true;
    this.indexingProgress = 0;

    try {
      Logger.info('Starting workspace indexing...');

      // Get all relevant files
      const files = await this.getWorkspaceFiles();
      Logger.info(`Found ${files.length} files to index`);

      // Start progress reporting
      if (this.progressReportingService) {
        const steps: ProgressStep[] = [
          {
            id: 'scan-files',
            name: 'Scanning Files',
            description: 'Finding relevant code files in workspace',
            status: 'completed',
            progress: 100
          },
          {
            id: 'analyze-files',
            name: 'Analyzing Code',
            description: `Processing ${files.length} files with AST analysis`,
            status: 'pending',
            progress: 0
          },
          {
            id: 'generate-embeddings',
            name: 'Generating Embeddings',
            description: 'Creating vector embeddings for semantic search',
            status: 'pending',
            progress: 0
          },
          {
            id: 'build-index',
            name: 'Building Index',
            description: 'Finalizing vector database index',
            status: 'pending',
            progress: 0
          }
        ];

        this.currentProgressSession = await this.progressReportingService.startProgress({
          title: '🔍 Indexing Codebase',
          description: `Building semantic search index for ${files.length} files`,
          cancellable: false,
          showInStatusBar: true,
          showInNotification: true,
          showInWebview: true,
          location: vscode.ProgressLocation.Notification
        }, steps);

        await this.progressReportingService.updateProgress({
          sessionId: this.currentProgressSession,
          stepId: 'analyze-files',
          status: 'running',
          message: 'Starting file analysis...'
        });
      }

      // Process files in batches
      const batchSize = 10;
      for (let i = 0; i < files.length; i += batchSize) {
        const batch = files.slice(i, i + batchSize);
        await this.processBatch(batch);

        this.indexingProgress = Math.round(((i + batch.length) / files.length) * 100);
        Logger.info(`Indexing progress: ${this.indexingProgress}%`);

        // Update progress
        if (this.progressReportingService && this.currentProgressSession) {
          const message = `Processed ${i + batch.length}/${files.length} files`;

          if (this.indexingProgress >= 80) {
            await this.progressReportingService.updateStep(this.currentProgressSession, 1, 'completed', 'File analysis completed');
            await this.progressReportingService.updateProgress({
              sessionId: this.currentProgressSession,
              stepId: 'generate-embeddings',
              status: 'running',
              message: 'Generating embeddings...'
            });
          }
        }
      }

      // Finalize indexing
      if (this.progressReportingService && this.currentProgressSession) {
        await this.progressReportingService.updateStep(this.currentProgressSession, 2, 'completed', 'Embeddings generated');
        await this.progressReportingService.updateProgress({
          sessionId: this.currentProgressSession,
          stepId: 'build-index',
          status: 'running',
          message: 'Building index...'
        });

        // Save database to disk
        await this.saveDatabase();

        await this.progressReportingService.updateStep(this.currentProgressSession, 3, 'completed', 'Index built and saved');
        await this.progressReportingService.completeProgress(this.currentProgressSession, true);
      }

      // Save database even if no progress reporting
      await this.saveDatabase();

      Logger.info(`Workspace indexing completed. Indexed ${this.embeddings.size} code elements.`);

    } catch (error) {
      Logger.error('Error during workspace indexing:', error);

      if (this.progressReportingService && this.currentProgressSession) {
        await this.progressReportingService.completeProgress(this.currentProgressSession, false);
      }
    } finally {
      this.isIndexing = false;
      this.indexingProgress = 100;
      this.currentProgressSession = undefined;
    }
  }

  /**
   * Process a batch of files
   */
  private async processBatch(files: string[]): Promise<void> {
    const promises = files.map(file => this.indexFile(file));
    await Promise.allSettled(promises);
  }

  /**
   * Index a single file
   */
  async indexFile(filePath: string): Promise<void> {
    try {
      Logger.debug(`Indexing file: ${filePath}`);

      // Analyze file with AST
      const analysis = await this.astAnalysisService.analyzeFile(filePath);
      
      if (analysis.errors.length > 0) {
        Logger.warn(`Errors analyzing ${filePath}:`, analysis.errors);
      }

      // Create embeddings for different code elements
      await this.createFileEmbedding(analysis);
      await this.createComponentEmbeddings(analysis);
      await this.createFunctionEmbeddings(analysis);
      await this.createClassEmbeddings(analysis);

    } catch (error) {
      Logger.error(`Error indexing file ${filePath}:`, error);
    }
  }

  /**
   * Create embedding for entire file
   */
  private async createFileEmbedding(analysis: FileAnalysis): Promise<void> {
    try {
      const content = await this.readFile(analysis.filePath);
      const summary = this.createFileSummary(analysis);
      
      const embedding = await this.generateEmbedding(summary);
      
      const codeEmbedding: CodeEmbedding = {
        id: `file:${analysis.filePath}`,
        filePath: analysis.filePath,
        content: summary,
        type: 'file',
        embedding,
        metadata: {
          language: analysis.language,
          framework: this.detectFramework(analysis),
          imports: analysis.imports.map(imp => imp.name || ''),
          exports: analysis.exports.map(exp => exp.name || ''),
          line: 1,
          size: content.length,
          lastModified: Date.now()
        }
      };

      this.embeddings.set(codeEmbedding.id, codeEmbedding);

    } catch (error) {
      Logger.error(`Error creating file embedding for ${analysis.filePath}:`, error);
    }
  }

  /**
   * Create embeddings for React components
   */
  private async createComponentEmbeddings(analysis: FileAnalysis): Promise<void> {
    for (const component of analysis.components) {
      try {
        const content = await this.extractComponentCode(analysis.filePath, component);
        const summary = this.createComponentSummary(component, content);
        
        const embedding = await this.generateEmbedding(summary);
        
        const codeEmbedding: CodeEmbedding = {
          id: `component:${analysis.filePath}:${component.name}`,
          filePath: analysis.filePath,
          content: summary,
          type: 'component',
          embedding,
          metadata: {
            name: component.name,
            language: analysis.language,
            framework: this.detectFramework(analysis),
            props: component.props,
            hooks: component.hooks,
            line: component.location.line,
            size: content.length,
            lastModified: Date.now()
          }
        };

        this.embeddings.set(codeEmbedding.id, codeEmbedding);

      } catch (error) {
        Logger.error(`Error creating component embedding for ${component.name}:`, error);
      }
    }
  }

  /**
   * Create embeddings for functions
   */
  private async createFunctionEmbeddings(analysis: FileAnalysis): Promise<void> {
    for (const func of analysis.functions) {
      try {
        const content = await this.extractFunctionCode(analysis.filePath, func);
        const summary = this.createFunctionSummary(func, content);
        
        const embedding = await this.generateEmbedding(summary);
        
        const codeEmbedding: CodeEmbedding = {
          id: `function:${analysis.filePath}:${func.name}`,
          filePath: analysis.filePath,
          content: summary,
          type: 'function',
          embedding,
          metadata: {
            name: func.name,
            language: analysis.language,
            line: func.line,
            size: content.length,
            lastModified: Date.now()
          }
        };

        this.embeddings.set(codeEmbedding.id, codeEmbedding);

      } catch (error) {
        Logger.error(`Error creating function embedding for ${func.name}:`, error);
      }
    }
  }

  /**
   * Create embeddings for classes
   */
  private async createClassEmbeddings(analysis: FileAnalysis): Promise<void> {
    for (const cls of analysis.classes) {
      try {
        const content = await this.extractClassCode(analysis.filePath, cls);
        const summary = this.createClassSummary(cls, content);
        
        const embedding = await this.generateEmbedding(summary);
        
        const codeEmbedding: CodeEmbedding = {
          id: `class:${analysis.filePath}:${cls.name}`,
          filePath: analysis.filePath,
          content: summary,
          type: 'class',
          embedding,
          metadata: {
            name: cls.name,
            language: analysis.language,
            line: cls.line,
            size: content.length,
            lastModified: Date.now()
          }
        };

        this.embeddings.set(codeEmbedding.id, codeEmbedding);

      } catch (error) {
        Logger.error(`Error creating class embedding for ${cls.name}:`, error);
      }
    }
  }

  /**
   * Perform semantic search
   */
  async semanticSearch(options: SemanticSearchOptions): Promise<SearchResult[]> {
    try {
      Logger.info(`Performing semantic search for: "${options.query}"`);

      // Generate embedding for search query
      const queryEmbedding = await this.generateEmbedding(options.query);

      // Calculate similarities
      const results: SearchResult[] = [];
      
      for (const [id, embedding] of this.embeddings) {
        // Filter by type if specified
        if (options.type && embedding.type !== options.type) {
          continue;
        }

        // Filter by language if specified
        if (options.language && embedding.metadata.language !== options.language) {
          continue;
        }

        // Filter by framework if specified
        if (options.framework && embedding.metadata.framework !== options.framework) {
          continue;
        }

        // Calculate cosine similarity
        const similarity = this.cosineSimilarity(queryEmbedding, embedding.embedding);
        
        // Apply threshold
        if (similarity >= (options.threshold || 0.5)) {
          results.push({
            embedding,
            similarity,
            relevance: this.calculateRelevance(embedding, options.query, similarity)
          });
        }
      }

      // Sort by relevance and limit results
      results.sort((a, b) => b.relevance - a.relevance);
      
      const limit = options.limit || 10;
      const limitedResults = results.slice(0, limit);

      Logger.info(`Found ${limitedResults.length} relevant results`);
      return limitedResults;

    } catch (error) {
      Logger.error('Error performing semantic search:', error);
      return [];
    }
  }

  /**
   * Generate embedding using OpenAI API (mock implementation)
   */
  private async generateEmbedding(text: string): Promise<number[]> {
    try {
      // This would use OpenAI's embedding API in production
      // For now, return a mock embedding
      const mockEmbedding = new Array(1536).fill(0).map(() => Math.random() - 0.5);
      
      // Normalize the embedding
      const magnitude = Math.sqrt(mockEmbedding.reduce((sum, val) => sum + val * val, 0));
      return mockEmbedding.map(val => val / magnitude);

    } catch (error) {
      Logger.error('Error generating embedding:', error);
      // Return zero vector as fallback
      return new Array(1536).fill(0);
    }
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  /**
   * Calculate relevance score
   */
  private calculateRelevance(embedding: CodeEmbedding, query: string, similarity: number): number {
    let relevance = similarity;

    // Boost relevance for exact name matches
    if (embedding.metadata.name && 
        embedding.metadata.name.toLowerCase().includes(query.toLowerCase())) {
      relevance += 0.2;
    }

    // Boost relevance for recent files
    const daysSinceModified = (Date.now() - embedding.metadata.lastModified) / (1000 * 60 * 60 * 24);
    if (daysSinceModified < 7) {
      relevance += 0.1;
    }

    // Boost relevance for smaller, focused code elements
    if (embedding.metadata.size < 1000) {
      relevance += 0.05;
    }

    return Math.min(relevance, 1.0);
  }

  /**
   * Utility methods
   */
  private async getWorkspaceFiles(): Promise<string[]> {
    const files: string[] = [];
    
    if (vscode.workspace.workspaceFolders) {
      for (const folder of vscode.workspace.workspaceFolders) {
        const pattern = new vscode.RelativePattern(folder, '**/*.{ts,tsx,js,jsx,css,scss,html,json}');
        const foundFiles = await vscode.workspace.findFiles(pattern, '**/node_modules/**');
        
        files.push(...foundFiles.map(uri => uri.fsPath));
      }
    }

    return files;
  }

  private async readFile(filePath: string): Promise<string> {
    const uri = vscode.Uri.file(filePath);
    const document = await vscode.workspace.openTextDocument(uri);
    return document.getText();
  }

  private detectFramework(analysis: FileAnalysis): string {
    const imports = analysis.imports.map(imp => imp.name || '').join(' ');
    
    if (imports.includes('react')) return 'react';
    if (imports.includes('vue')) return 'vue';
    if (imports.includes('@angular')) return 'angular';
    if (imports.includes('svelte')) return 'svelte';
    
    return 'vanilla';
  }

  private createFileSummary(analysis: FileAnalysis): string {
    const parts = [
      `File: ${analysis.filePath}`,
      `Language: ${analysis.language}`,
      `Components: ${analysis.components.map(c => c.name).join(', ')}`,
      `Functions: ${analysis.functions.map(f => f.name).join(', ')}`,
      `Classes: ${analysis.classes.map(c => c.name).join(', ')}`,
      `Imports: ${analysis.imports.map(i => i.name).join(', ')}`,
      `Exports: ${analysis.exports.map(e => e.name).join(', ')}`
    ];
    
    return parts.filter(part => !part.endsWith(': ')).join('\n');
  }

  private createComponentSummary(component: ComponentInfo, content: string): string {
    return [
      `React ${component.type} component: ${component.name}`,
      `Props: ${component.props?.join(', ') || 'none'}`,
      `Hooks: ${component.hooks?.join(', ') || 'none'}`,
      `Code: ${content.substring(0, 500)}...`
    ].join('\n');
  }

  private createFunctionSummary(func: any, content: string): string {
    return [
      `Function: ${func.name}`,
      `Type: ${func.type}`,
      `Code: ${content.substring(0, 500)}...`
    ].join('\n');
  }

  private createClassSummary(cls: any, content: string): string {
    return [
      `Class: ${cls.name}`,
      `Type: ${cls.type}`,
      `Code: ${content.substring(0, 500)}...`
    ].join('\n');
  }

  private async extractComponentCode(filePath: string, component: ComponentInfo): Promise<string> {
    const content = await this.readFile(filePath);
    const lines = content.split('\n');
    
    // Extract component code based on location
    const startLine = Math.max(0, component.location.line - 1);
    const endLine = Math.min(lines.length, startLine + 50); // Limit to 50 lines
    
    return lines.slice(startLine, endLine).join('\n');
  }

  private async extractFunctionCode(filePath: string, func: any): Promise<string> {
    const content = await this.readFile(filePath);
    const lines = content.split('\n');
    
    const startLine = Math.max(0, func.line - 1);
    const endLine = Math.min(lines.length, startLine + 30); // Limit to 30 lines
    
    return lines.slice(startLine, endLine).join('\n');
  }

  private async extractClassCode(filePath: string, cls: any): Promise<string> {
    const content = await this.readFile(filePath);
    const lines = content.split('\n');
    
    const startLine = Math.max(0, cls.line - 1);
    const endLine = Math.min(lines.length, startLine + 100); // Limit to 100 lines
    
    return lines.slice(startLine, endLine).join('\n');
  }



  /**
   * Clear all embeddings
   */
  clearIndex(): void {
    this.embeddings.clear();
    Logger.info('Vector database index cleared');
  }
}
