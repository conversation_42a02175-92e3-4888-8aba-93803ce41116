# 🧪 UIOrbit Testing Guide

## 🚀 Quick Start Testing

### 1. **Load Extension in VS Code**

```bash
# Navigate to UIOrbit directory
cd uiorbit

# Ensure compilation is successful
npm run compile

# Open in VS Code Extension Development Host
# Press F5 in VS Code or:
code --extensionDevelopmentPath=. --new-window
```

### 2. **Basic Functionality Test**

1. **Open Command Palette** (`Ctrl+Shift+P`)
2. **Look for UIOrbit commands:**
   - `UIOrbit: Open Chat`
   - `UIOrbit: Analyze Design System`
   - `UIOrbit: Generate Component`

3. **Test Chat Interface:**
   - Click UIOrbit icon in sidebar
   - Try basic prompts:
     - "Hello UIOrbit"
     - "Analyze my project structure"
     - "Generate a button component"

### 3. **Service Integration Test**

Test each major service:

#### **AI Service** 🤖
```typescript
// In chat, try:
"Generate a modern React button component with TypeScript"
"Create a responsive navigation bar"
"Build a card component with hover effects"
```

#### **Design System Analysis** 🎨
```typescript
// In chat, try:
"Analyze my design system"
"What colors are used in my project?"
"Extract typography tokens"
"Show me spacing patterns"
```

#### **Figma Integration** 🎯
```typescript
// In chat, try:
"Convert this Figma design to React"
// (Upload a Figma JSON export)
"Generate components from Figma file"
```

#### **Agentic Workflows** 🔄
```typescript
// In chat, try complex requests:
"Create a complete dashboard with header, sidebar, and main content"
"Build a form with validation and submit handling"
"Generate a landing page with hero section and features"
```

### 4. **Performance Monitoring Test**

1. **Check VS Code Output Panel**
   - Select "UIOrbit" from dropdown
   - Monitor service initialization logs
   - Watch for performance metrics

2. **Monitor Memory Usage**
   - Open VS Code Developer Tools (`Help > Toggle Developer Tools`)
   - Check memory usage during operations
   - Verify no memory leaks

### 5. **Error Handling Test**

Test error scenarios:

1. **Invalid API Key**
   - Remove OpenAI API key
   - Try generating code
   - Verify graceful error handling

2. **Large File Analysis**
   - Open large project
   - Test workspace analysis
   - Verify performance remains good

3. **Network Issues**
   - Disconnect internet
   - Try AI operations
   - Verify fallback behavior

## 🔧 Configuration Setup

### 1. **Environment Variables**

Create `.env` file in `uiorbit/` directory:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional
DEFAULT_FRAMEWORK=react
DEFAULT_STYLING=tailwind
DEBUG_MODE=true
ENABLE_ACCESSIBILITY=true
ENABLE_RESPONSIVE_DESIGN=true
API_RATE_LIMIT_PER_MINUTE=60
MAX_CONCURRENT_REQUESTS=5

# Figma (Optional)
FIGMA_API_KEY=your_figma_api_key_here
```

### 2. **VS Code Settings**

Add to VS Code settings:

```json
{
  "uiorbit.openaiApiKey": "your_api_key_here",
  "uiorbit.defaultFramework": "react",
  "uiorbit.defaultStyling": "tailwind",
  "uiorbit.debugMode": true,
  "uiorbit.enableAccessibility": true,
  "uiorbit.enableResponsiveDesign": true
}
```

## 🧪 Test Scenarios

### **Scenario 1: Component Generation**
1. Open a React project
2. Ask: "Generate a modern button component with variants"
3. Verify:
   - Code is generated
   - File is created
   - TypeScript types are included
   - Styling is applied

### **Scenario 2: Design System Analysis**
1. Open project with CSS/SCSS files
2. Ask: "Analyze my design system"
3. Verify:
   - Colors are extracted
   - Typography is analyzed
   - Spacing patterns are identified
   - Report is generated

### **Scenario 3: Complex Workflow**
1. Ask: "Create a complete user profile page"
2. Verify:
   - Task plan is created
   - Multiple subtasks are generated
   - Progress is shown
   - Files are created in sequence

### **Scenario 4: Figma Integration**
1. Export Figma design as JSON
2. Upload to UIOrbit
3. Ask: "Convert this to React components"
4. Verify:
   - Design is parsed
   - Components are generated
   - Styling is preserved

## 📊 Performance Benchmarks

### **Expected Performance:**
- **Service Initialization:** < 5 seconds
- **File Analysis:** < 2 seconds per file
- **Code Generation:** < 10 seconds
- **Design System Analysis:** < 30 seconds
- **Memory Usage:** < 200MB additional

### **Performance Monitoring:**
```typescript
// Check performance metrics in console
// Look for logs like:
"Performance: DesignSystemAnalyzer.analyzeWorkspace completed in 1234ms"
"Memory usage: 150MB heap used"
"Task execution: Component generation completed in 8765ms"
```

## 🐛 Troubleshooting

### **Common Issues:**

1. **Extension Not Loading**
   - Check compilation errors: `npm run compile`
   - Verify all dependencies: `npm install`
   - Check VS Code output panel for errors

2. **AI Service Not Working**
   - Verify OpenAI API key is set
   - Check network connectivity
   - Monitor rate limits

3. **File Operations Failing**
   - Check workspace permissions
   - Verify file paths are correct
   - Check disk space

4. **Performance Issues**
   - Monitor memory usage
   - Check for infinite loops in file watching
   - Verify AST parsing is not stuck

### **Debug Mode:**
Enable debug mode for detailed logging:
```env
DEBUG_MODE=true
```

### **Log Locations:**
- **VS Code Output Panel:** UIOrbit channel
- **Developer Console:** Browser dev tools
- **Extension Host:** VS Code extension development console

## ✅ Success Criteria

### **Basic Functionality:** ✅
- [ ] Extension loads without errors
- [ ] Chat interface opens
- [ ] Services initialize properly
- [ ] Basic commands work

### **AI Integration:** ✅
- [ ] Code generation works
- [ ] API calls succeed
- [ ] Error handling works
- [ ] Rate limiting respected

### **File Operations:** ✅
- [ ] Files can be read
- [ ] Files can be written
- [ ] Workspace analysis works
- [ ] File watching functions

### **Performance:** ✅
- [ ] Memory usage reasonable
- [ ] Response times acceptable
- [ ] No memory leaks
- [ ] Smooth user experience

### **Error Handling:** ✅
- [ ] Graceful error messages
- [ ] No crashes
- [ ] Fallback behavior works
- [ ] User feedback provided

## 🎯 Next Steps After Testing

1. **Gather Feedback** - Document any issues found
2. **Performance Optimization** - Address any bottlenecks
3. **Feature Refinement** - Improve based on usage
4. **Documentation Updates** - Update based on testing results
5. **Beta Release Preparation** - Prepare for wider testing

**Happy Testing! 🚀**
