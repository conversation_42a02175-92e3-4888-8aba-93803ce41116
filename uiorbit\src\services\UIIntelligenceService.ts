import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';
import { AIService } from './AIService';
import { VectorDatabaseService } from './VectorDatabaseService';
import { DesignSystemAnalyzer } from './DesignSystemAnalyzer';

export interface UITrend {
  name: string;
  description: string;
  popularity: number;
  category: 'layout' | 'animation' | 'color' | 'typography' | 'interaction' | 'component';
  examples: string[];
  implementation: string;
  frameworks: string[];
}

export interface UIRecommendation {
  type: 'improvement' | 'modernization' | 'accessibility' | 'performance';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  code?: string;
  resources?: string[];
}

export interface ComponentSuggestion {
  name: string;
  description: string;
  props: string[];
  variants: string[];
  useCases: string[];
  code: string;
  styling: string;
}

/**
 * UI Intelligence Service - Advanced UI/UX analysis and recommendations
 * Provides intelligent insights about modern UI trends, design patterns, and improvements
 */
export class UIIntelligenceService {
  private aiService: AIService;
  private vectorDatabaseService: VectorDatabaseService;
  private designSystemAnalyzer: DesignSystemAnalyzer;
  private trendCache: Map<string, UITrend[]> = new Map();
  private lastTrendUpdate: number = 0;
  private readonly TREND_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

  constructor(
    aiService: AIService,
    vectorDatabaseService: VectorDatabaseService,
    designSystemAnalyzer: DesignSystemAnalyzer
  ) {
    this.aiService = aiService;
    this.vectorDatabaseService = vectorDatabaseService;
    this.designSystemAnalyzer = designSystemAnalyzer;
  }

  /**
   * Get current UI/UX trends based on 2024-2025 data
   */
  async getCurrentTrends(): Promise<UITrend[]> {
    try {
      const cacheKey = 'current-trends';
      const cached = this.trendCache.get(cacheKey);
      
      if (cached && Date.now() - this.lastTrendUpdate < this.TREND_CACHE_DURATION) {
        return cached;
      }

      const trends: UITrend[] = [
        {
          name: 'Glassmorphism 2.0',
          description: 'Enhanced glass-like UI elements with improved accessibility and performance',
          popularity: 85,
          category: 'layout',
          examples: ['macOS Big Sur', 'iOS 15+', 'Windows 11'],
          implementation: 'backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.1);',
          frameworks: ['React', 'Vue', 'Angular']
        },
        {
          name: 'Micro-interactions',
          description: 'Subtle animations that provide feedback and enhance user experience',
          popularity: 92,
          category: 'animation',
          examples: ['Button hover states', 'Loading animations', 'Form validation'],
          implementation: 'CSS transitions, Framer Motion, GSAP',
          frameworks: ['React', 'Vue', 'Angular', 'Svelte']
        },
        {
          name: 'Dark Mode First',
          description: 'Designing for dark mode as the primary interface with light mode as alternative',
          popularity: 78,
          category: 'color',
          examples: ['GitHub', 'Discord', 'Slack'],
          implementation: 'CSS custom properties, system preference detection',
          frameworks: ['All frameworks']
        },
        {
          name: 'Brutalist Web Design',
          description: 'Bold, raw, and intentionally unpolished design aesthetic',
          popularity: 65,
          category: 'layout',
          examples: ['Gumroad', 'Hype4', 'Bloomberg'],
          implementation: 'Bold typography, high contrast, minimal styling',
          frameworks: ['All frameworks']
        },
        {
          name: 'Voice User Interface',
          description: 'Integration of voice commands and speech recognition',
          popularity: 70,
          category: 'interaction',
          examples: ['Web Speech API', 'Voice search', 'Audio feedback'],
          implementation: 'Web Speech API, voice recognition libraries',
          frameworks: ['React', 'Vue', 'Vanilla JS']
        },
        {
          name: 'Neumorphism Evolution',
          description: 'Refined soft UI with better contrast and accessibility',
          popularity: 60,
          category: 'layout',
          examples: ['Soft buttons', 'Card designs', 'Form elements'],
          implementation: 'box-shadow inset and outset, subtle gradients',
          frameworks: ['All frameworks']
        }
      ];

      this.trendCache.set(cacheKey, trends);
      this.lastTrendUpdate = Date.now();
      
      return trends;
    } catch (error) {
      Logger.error('Error getting current trends:', error);
      return [];
    }
  }

  /**
   * Analyze project and provide UI/UX recommendations
   */
  async analyzeProjectUI(): Promise<UIRecommendation[]> {
    try {
      Logger.info('Analyzing project UI/UX...');

      const designSystem = await this.designSystemAnalyzer.analyzeWorkspaceDesignSystem();
      const recommendations: UIRecommendation[] = [];

      // Analyze color scheme
      if (designSystem.tokens.colors && designSystem.tokens.colors.primary.length > 0) {
        const allColors = [
          ...designSystem.tokens.colors.primary,
          ...designSystem.tokens.colors.secondary,
          ...designSystem.tokens.colors.neutral
        ];
        const hasLightColors = allColors.some((color: any) =>
          this.isLightColor(color.value)
        );
        const hasDarkColors = allColors.some((color: any) =>
          this.isDarkColor(color.value)
        );

        if (!hasLightColors || !hasDarkColors) {
          recommendations.push({
            type: 'modernization',
            title: 'Add Dark Mode Support',
            description: 'Consider implementing dark mode to follow current UI trends and improve user experience',
            impact: 'high',
            effort: 'medium',
            code: `
// CSS Custom Properties for Dark Mode
:root {
  --bg-primary: #ffffff;
  --text-primary: #000000;
}

[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --text-primary: #ffffff;
}
            `,
            resources: ['https://web.dev/prefers-color-scheme/']
          });
        }
      }

      // Analyze typography
      const fontFamilyCount = Object.keys(designSystem.tokens.typography.fontFamilies).length;
      if (fontFamilyCount > 5) {
        recommendations.push({
          type: 'improvement',
          title: 'Simplify Typography Scale',
          description: 'Too many font variations can hurt consistency. Consider using a systematic type scale',
          impact: 'medium',
          effort: 'low',
          code: `
// Systematic Type Scale
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
          `
        });
      }

      // Check for accessibility
      recommendations.push({
        type: 'accessibility',
        title: 'Enhance Accessibility',
        description: 'Add ARIA labels, focus states, and keyboard navigation support',
        impact: 'high',
        effort: 'medium',
        code: `
// Accessible Button Example
<button 
  aria-label="Close dialog"
  className="focus:ring-2 focus:ring-blue-500"
  onKeyDown={(e) => e.key === 'Enter' && handleClick()}
>
  ×
</button>
        `
      });

      // Performance recommendations
      recommendations.push({
        type: 'performance',
        title: 'Optimize Animations',
        description: 'Use CSS transforms and opacity for smooth 60fps animations',
        impact: 'medium',
        effort: 'low',
        code: `
// Performant Animation
.animate-slide {
  transform: translateX(-100%);
  transition: transform 0.3s ease-out;
}

.animate-slide.active {
  transform: translateX(0);
}
        `
      });

      Logger.info(`Generated ${recommendations.length} UI recommendations`);
      return recommendations;

    } catch (error) {
      Logger.error('Error analyzing project UI:', error);
      return [];
    }
  }

  /**
   * Suggest modern components based on project context
   */
  async suggestComponents(): Promise<ComponentSuggestion[]> {
    try {
      const suggestions: ComponentSuggestion[] = [
        {
          name: 'GlassCard',
          description: 'Modern glassmorphism card component with backdrop blur',
          props: ['children', 'className', 'blur', 'opacity'],
          variants: ['default', 'elevated', 'bordered'],
          useCases: ['Feature cards', 'Modal overlays', 'Navigation panels'],
          code: `
export const GlassCard = ({ children, className = '', blur = 10, opacity = 0.1 }) => (
  <div 
    className={\`backdrop-blur-\${blur} bg-white/\${opacity * 100} border border-white/20 rounded-xl p-6 \${className}\`}
    style={{ backdropFilter: \`blur(\${blur}px)\` }}
  >
    {children}
  </div>
);
          `,
          styling: 'Tailwind CSS with backdrop-filter utilities'
        },
        {
          name: 'AnimatedButton',
          description: 'Button with micro-interactions and loading states',
          props: ['children', 'loading', 'variant', 'size', 'onClick'],
          variants: ['primary', 'secondary', 'ghost', 'destructive'],
          useCases: ['Form submissions', 'CTAs', 'Navigation'],
          code: `
export const AnimatedButton = ({ children, loading, variant = 'primary', ...props }) => (
  <button 
    className={\`
      relative overflow-hidden transition-all duration-200 
      hover:scale-105 active:scale-95 
      \${variant === 'primary' ? 'bg-blue-600 hover:bg-blue-700' : ''}
      disabled:opacity-50 disabled:cursor-not-allowed
    \`}
    disabled={loading}
    {...props}
  >
    {loading && <Spinner className="absolute inset-0" />}
    <span className={loading ? 'opacity-0' : 'opacity-100'}>
      {children}
    </span>
  </button>
);
          `,
          styling: 'Tailwind CSS with custom animations'
        }
      ];

      return suggestions;
    } catch (error) {
      Logger.error('Error suggesting components:', error);
      return [];
    }
  }

  /**
   * Generate AI-powered UI improvements
   */
  async generateUIImprovements(componentCode: string): Promise<string> {
    try {
      const prompt = `
Analyze this React component and suggest modern UI/UX improvements:

\`\`\`tsx
${componentCode}
\`\`\`

Please provide:
1. Modern design patterns to apply
2. Accessibility improvements
3. Performance optimizations
4. Current UI trends integration
5. Updated code with improvements

Focus on 2024-2025 UI trends like glassmorphism, micro-interactions, and accessibility.
      `;

      const response = await this.aiService.processMessage(prompt);
      return response.content;

    } catch (error) {
      Logger.error('Error generating UI improvements:', error);
      return 'Failed to generate UI improvements. Please try again.';
    }
  }

  /**
   * Check if a color is light
   */
  private isLightColor(color: string): boolean {
    // Simple lightness check - can be enhanced
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128;
  }

  /**
   * Check if a color is dark
   */
  private isDarkColor(color: string): boolean {
    return !this.isLightColor(color);
  }

  /**
   * Clear trend cache
   */
  clearCache(): void {
    this.trendCache.clear();
    this.lastTrendUpdate = 0;
  }
}
