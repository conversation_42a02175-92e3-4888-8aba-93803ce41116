import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';
import { ServiceRegistry } from '../core/ServiceRegistry';

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: Date;
  category: 'performance' | 'memory' | 'usage' | 'error';
  metadata?: { [key: string]: any };
}

export interface PerformanceSession {
  id: string;
  name: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  metrics: PerformanceMetric[];
  status: 'running' | 'completed' | 'failed';
}

export interface TelemetryEvent {
  event: string;
  properties: { [key: string]: any };
  measurements: { [key: string]: number };
  timestamp: Date;
  sessionId?: string;
  userId?: string;
}

export interface PerformanceReport {
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    totalSessions: number;
    averageDuration: number;
    successRate: number;
    errorRate: number;
  };
  metrics: {
    performance: PerformanceMetric[];
    memory: PerformanceMetric[];
    usage: PerformanceMetric[];
    errors: PerformanceMetric[];
  };
  trends: {
    [metricName: string]: {
      trend: 'improving' | 'degrading' | 'stable';
      change: number;
    };
  };
}

export class PerformanceMonitoringService {
  private sessions: Map<string, PerformanceSession> = new Map();
  private metrics: PerformanceMetric[] = [];
  private telemetryEvents: TelemetryEvent[] = [];
  private isEnabled: boolean = true;
  private maxMetricsHistory: number = 10000;
  private maxTelemetryHistory: number = 5000;

  constructor(private serviceRegistry: ServiceRegistry) {
    this.initializeMonitoring();
  }

  /**
   * Start a performance monitoring session
   */
  startSession(name: string): string {
    const sessionId = this.generateId();
    
    const session: PerformanceSession = {
      id: sessionId,
      name,
      startTime: new Date(),
      metrics: [],
      status: 'running'
    };

    this.sessions.set(sessionId, session);
    
    this.recordTelemetry('session_started', {
      sessionName: name,
      sessionId
    }, {});

    Logger.info(`Performance session started: ${name} (${sessionId})`);
    return sessionId;
  }

  /**
   * End a performance monitoring session
   */
  endSession(sessionId: string, status: 'completed' | 'failed' = 'completed'): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      Logger.warn(`Performance session not found: ${sessionId}`);
      return;
    }

    session.endTime = new Date();
    session.duration = session.endTime.getTime() - session.startTime.getTime();
    session.status = status;

    this.recordTelemetry('session_ended', {
      sessionName: session.name,
      sessionId,
      status
    }, {
      duration: session.duration
    });

    Logger.info(`Performance session ended: ${session.name} (${session.duration}ms)`);
  }

  /**
   * Record a performance metric
   */
  recordMetric(
    name: string,
    value: number,
    unit: 'ms' | 'bytes' | 'count' | 'percentage',
    category: 'performance' | 'memory' | 'usage' | 'error' = 'performance',
    sessionId?: string,
    metadata?: { [key: string]: any }
  ): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      category,
      metadata
    };

    this.metrics.push(metric);

    // Add to session if specified
    if (sessionId) {
      const session = this.sessions.get(sessionId);
      if (session) {
        session.metrics.push(metric);
      }
    }

    // Cleanup old metrics
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    // Log significant metrics
    if (category === 'error' || (category === 'performance' && value > 5000)) {
      Logger.warn(`Performance metric: ${name} = ${value}${unit}`);
    }
  }

  /**
   * Record telemetry event
   */
  recordTelemetry(
    event: string,
    properties: { [key: string]: any } = {},
    measurements: { [key: string]: number } = {},
    sessionId?: string
  ): void {
    if (!this.isEnabled) return;

    const telemetryEvent: TelemetryEvent = {
      event,
      properties,
      measurements,
      timestamp: new Date(),
      sessionId,
      userId: this.getUserId()
    };

    this.telemetryEvents.push(telemetryEvent);

    // Cleanup old events
    if (this.telemetryEvents.length > this.maxTelemetryHistory) {
      this.telemetryEvents = this.telemetryEvents.slice(-this.maxTelemetryHistory);
    }
  }

  /**
   * Measure execution time of a function
   */
  async measureExecution<T>(
    name: string,
    fn: () => Promise<T>,
    sessionId?: string
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      this.recordMetric(name, duration, 'ms', 'performance', sessionId);
      this.recordTelemetry('function_executed', {
        functionName: name,
        success: true
      }, {
        duration
      }, sessionId);

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.recordMetric(`${name}_error`, duration, 'ms', 'error', sessionId);
      this.recordTelemetry('function_executed', {
        functionName: name,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }, {
        duration
      }, sessionId);

      throw error;
    }
  }

  /**
   * Monitor memory usage
   */
  recordMemoryUsage(sessionId?: string): void {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const memUsage = process.memoryUsage();
      
      this.recordMetric('memory_heap_used', memUsage.heapUsed, 'bytes', 'memory', sessionId);
      this.recordMetric('memory_heap_total', memUsage.heapTotal, 'bytes', 'memory', sessionId);
      this.recordMetric('memory_external', memUsage.external, 'bytes', 'memory', sessionId);
      this.recordMetric('memory_rss', memUsage.rss, 'bytes', 'memory', sessionId);
    }
  }

  /**
   * Get performance report for a time period
   */
  getPerformanceReport(startDate: Date, endDate: Date): PerformanceReport {
    const periodMetrics = this.metrics.filter(m => 
      m.timestamp >= startDate && m.timestamp <= endDate
    );

    const periodSessions = Array.from(this.sessions.values()).filter(s =>
      s.startTime >= startDate && s.startTime <= endDate
    );

    const completedSessions = periodSessions.filter(s => s.status === 'completed');
    const failedSessions = periodSessions.filter(s => s.status === 'failed');

    const averageDuration = completedSessions.length > 0
      ? completedSessions.reduce((sum, s) => sum + (s.duration || 0), 0) / completedSessions.length
      : 0;

    const successRate = periodSessions.length > 0
      ? (completedSessions.length / periodSessions.length) * 100
      : 0;

    const errorRate = periodSessions.length > 0
      ? (failedSessions.length / periodSessions.length) * 100
      : 0;

    return {
      period: { start: startDate, end: endDate },
      summary: {
        totalSessions: periodSessions.length,
        averageDuration,
        successRate,
        errorRate
      },
      metrics: {
        performance: periodMetrics.filter(m => m.category === 'performance'),
        memory: periodMetrics.filter(m => m.category === 'memory'),
        usage: periodMetrics.filter(m => m.category === 'usage'),
        errors: periodMetrics.filter(m => m.category === 'error')
      },
      trends: this.calculateTrends(periodMetrics)
    };
  }

  /**
   * Get current session metrics
   */
  getSessionMetrics(sessionId: string): PerformanceMetric[] {
    const session = this.sessions.get(sessionId);
    return session ? session.metrics : [];
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(): PerformanceSession[] {
    return Array.from(this.sessions.values()).filter(s => s.status === 'running');
  }

  /**
   * Enable or disable monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    Logger.info(`Performance monitoring ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Clear all metrics and sessions
   */
  clear(): void {
    this.metrics = [];
    this.sessions.clear();
    this.telemetryEvents = [];
    Logger.info('Performance monitoring data cleared');
  }

  /**
   * Export metrics for external analysis
   */
  exportMetrics(): {
    metrics: PerformanceMetric[];
    sessions: PerformanceSession[];
    telemetry: TelemetryEvent[];
  } {
    return {
      metrics: [...this.metrics],
      sessions: Array.from(this.sessions.values()),
      telemetry: [...this.telemetryEvents]
    };
  }

  private initializeMonitoring(): void {
    // Start periodic memory monitoring
    setInterval(() => {
      this.recordMemoryUsage();
    }, 30000); // Every 30 seconds

    // Monitor VS Code events
    if (vscode.window.onDidChangeActiveTextEditor) {
      vscode.window.onDidChangeActiveTextEditor(() => {
        this.recordTelemetry('editor_changed', {}, {});
      });
    }

    Logger.info('Performance monitoring initialized');
  }

  private calculateTrends(metrics: PerformanceMetric[]): { [metricName: string]: any } {
    const trends: { [metricName: string]: any } = {};
    
    // Group metrics by name
    const metricGroups = metrics.reduce((groups, metric) => {
      if (!groups[metric.name]) {
        groups[metric.name] = [];
      }
      groups[metric.name].push(metric);
      return groups;
    }, {} as { [name: string]: PerformanceMetric[] });

    // Calculate trends for each metric
    Object.entries(metricGroups).forEach(([name, metricList]) => {
      if (metricList.length < 2) {
        trends[name] = { trend: 'stable', change: 0 };
        return;
      }

      // Sort by timestamp
      metricList.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      
      const firstHalf = metricList.slice(0, Math.floor(metricList.length / 2));
      const secondHalf = metricList.slice(Math.floor(metricList.length / 2));

      const firstAvg = firstHalf.reduce((sum, m) => sum + m.value, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((sum, m) => sum + m.value, 0) / secondHalf.length;

      const change = ((secondAvg - firstAvg) / firstAvg) * 100;
      
      let trend: 'improving' | 'degrading' | 'stable';
      if (Math.abs(change) < 5) {
        trend = 'stable';
      } else if (change > 0) {
        trend = name.includes('error') ? 'degrading' : 'improving';
      } else {
        trend = name.includes('error') ? 'improving' : 'degrading';
      }

      trends[name] = { trend, change };
    });

    return trends;
  }

  private getUserId(): string {
    // Generate or retrieve anonymous user ID
    return 'anonymous-user';
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
}

// Performance monitoring decorators
export function MonitorPerformance(metricName?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const name = metricName || `${target.constructor.name}.${propertyName}`;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      
      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;
        
        // Record metric if performance service is available
        // This would need to be injected or accessed globally
        Logger.info(`Performance: ${name} completed in ${duration}ms`);
        
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        Logger.warn(`Performance: ${name} failed after ${duration}ms`);
        throw error;
      }
    };

    return descriptor;
  };
}
