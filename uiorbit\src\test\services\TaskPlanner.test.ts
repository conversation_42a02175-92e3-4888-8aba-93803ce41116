import { TestRunner, TestSuite, TestAsser<PERSON>, MockService, TestDataGenerator } from '../TestRunner';
import { TaskPlannerService, PlanningContext } from '../../services/TaskPlannerService';
import { ServiceRegistry } from '../../core/ServiceRegistry';
import { AIService } from '../../services/AIService';

export function createTaskPlannerTests(): TestSuite {
  const mockService = new MockService();
  let serviceRegistry: ServiceRegistry;
  let taskPlanner: TaskPlannerService;

  return {
    name: 'TaskPlanner',
    description: 'Tests for the Task Planner service',
    
    setup: async () => {
      serviceRegistry = new ServiceRegistry();
      
      // Mock AI Service
      const mockAI = {
        generateCode: async (prompt: string, options: any) => {
          if (prompt.includes('intent')) {
            return JSON.stringify({
              intent: 'component-generation',
              complexity: 'medium',
              requiredServices: ['aiService', 'fileOperations'],
              estimatedTasks: 3,
              mainActions: ['analyze', 'generate', 'validate'],
              outputType: 'component'
            });
          }
          
          if (prompt.includes('task breakdown')) {
            return JSON.stringify([
              {
                name: 'Analyze Requirements',
                description: 'Analyze user requirements and project context',
                type: 'analysis',
                dependencies: [],
                estimatedDuration: 3000,
                serviceRequired: 'contextEngine'
              },
              {
                name: 'Generate Component',
                description: 'Generate component code based on requirements',
                type: 'generation',
                dependencies: ['Analyze Requirements'],
                estimatedDuration: 8000,
                serviceRequired: 'aiService'
              },
              {
                name: 'Validate Output',
                description: 'Validate generated code for syntax and best practices',
                type: 'validation',
                dependencies: ['Generate Component'],
                estimatedDuration: 2000,
                serviceRequired: 'astAnalysis'
              }
            ]);
          }
          
          return 'Mock response';
        },
        analyzeImage: async (imageUrl: string, prompt: string) => 'Mock image analysis',
        chat: async (message: string, context?: any) => ({ response: 'Mock chat response' })
      };

      serviceRegistry.register('ai', mockAI);
      taskPlanner = new TaskPlannerService(serviceRegistry);
    },

    teardown: async () => {
      mockService.clear();
    },

    tests: [
      {
        name: 'should create a task plan from user prompt',
        description: 'Test basic task plan creation functionality',
        test: async () => {
          const context: PlanningContext = {
            userPrompt: 'Create a modern button component with hover animations',
            projectContext: {},
            availableServices: ['aiService', 'fileOperations', 'astAnalysis'],
            constraints: {
              maxTasks: 10,
              maxDuration: 60000,
              allowFileModification: true,
              allowNetworkAccess: false
            }
          };

          const plan = await taskPlanner.createPlan(context);

          TestAssertions.assertNotUndefined(plan);
          TestAssertions.assertNotUndefined(plan.id);
          TestAssertions.assertEqual(plan.status, 'pending');
          TestAssertions.assertTrue(plan.tasks.length > 0);
          TestAssertions.assertTrue(plan.totalEstimatedDuration > 0);
        }
      },

      {
        name: 'should handle task dependencies correctly',
        description: 'Test task dependency resolution and ordering',
        test: async () => {
          const context: PlanningContext = {
            userPrompt: 'Generate a component with tests',
            projectContext: {},
            availableServices: ['aiService', 'fileOperations'],
            constraints: {
              maxTasks: 5,
              maxDuration: 30000,
              allowFileModification: true,
              allowNetworkAccess: false
            }
          };

          const plan = await taskPlanner.createPlan(context);
          
          // Check that tasks are properly ordered based on dependencies
          const analyzeTask = plan.tasks.find(t => t.name === 'Analyze Requirements');
          const generateTask = plan.tasks.find(t => t.name === 'Generate Component');
          const validateTask = plan.tasks.find(t => t.name === 'Validate Output');

          TestAssertions.assertNotUndefined(analyzeTask);
          TestAssertions.assertNotUndefined(generateTask);
          TestAssertions.assertNotUndefined(validateTask);

          // Check dependencies
          TestAssertions.assertEqual(analyzeTask!.dependencies.length, 0);
          TestAssertions.assertContains(generateTask!.dependencies, 'Analyze Requirements');
          TestAssertions.assertContains(validateTask!.dependencies, 'Generate Component');
        }
      },

      {
        name: 'should retrieve plans by ID',
        description: 'Test plan storage and retrieval',
        test: async () => {
          const context: PlanningContext = {
            userPrompt: 'Test plan retrieval',
            projectContext: {},
            availableServices: ['aiService'],
            constraints: {
              maxTasks: 3,
              maxDuration: 15000,
              allowFileModification: false,
              allowNetworkAccess: false
            }
          };

          const plan = await taskPlanner.createPlan(context);
          const retrievedPlan = taskPlanner.getPlan(plan.id);

          TestAssertions.assertNotUndefined(retrievedPlan);
          TestAssertions.assertEqual(retrievedPlan!.id, plan.id);
          TestAssertions.assertEqual(retrievedPlan!.tasks.length, plan.tasks.length);
        }
      },

      {
        name: 'should cancel plans correctly',
        description: 'Test plan cancellation functionality',
        test: async () => {
          const context: PlanningContext = {
            userPrompt: 'Test plan cancellation',
            projectContext: {},
            availableServices: ['aiService'],
            constraints: {
              maxTasks: 2,
              maxDuration: 10000,
              allowFileModification: false,
              allowNetworkAccess: false
            }
          };

          const plan = await taskPlanner.createPlan(context);
          const cancelled = await taskPlanner.cancelPlan(plan.id);

          TestAssertions.assertTrue(cancelled);
          
          const retrievedPlan = taskPlanner.getPlan(plan.id);
          TestAssertions.assertEqual(retrievedPlan!.status, 'cancelled');
          
          // All tasks should be cancelled
          retrievedPlan!.tasks.forEach(task => {
            TestAssertions.assertEqual(task.status, 'cancelled');
          });
        }
      },

      {
        name: 'should update plan progress correctly',
        description: 'Test progress tracking functionality',
        test: async () => {
          const context: PlanningContext = {
            userPrompt: 'Test progress tracking',
            projectContext: {},
            availableServices: ['aiService'],
            constraints: {
              maxTasks: 3,
              maxDuration: 15000,
              allowFileModification: false,
              allowNetworkAccess: false
            }
          };

          const plan = await taskPlanner.createPlan(context);
          
          // Simulate task completion
          plan.tasks[0].status = 'completed';
          plan.tasks[1].status = 'running';
          
          taskPlanner.updatePlanProgress(plan.id);
          
          const updatedPlan = taskPlanner.getPlan(plan.id);
          TestAssertions.assertEqual(updatedPlan!.progress.completedTasks, 1);
          TestAssertions.assertEqual(updatedPlan!.status, 'running');
          TestAssertions.assertNotUndefined(updatedPlan!.progress.currentTask);
        }
      },

      {
        name: 'should get all active plans',
        description: 'Test retrieval of all active plans',
        test: async () => {
          const context1: PlanningContext = {
            userPrompt: 'First plan',
            projectContext: {},
            availableServices: ['aiService'],
            constraints: {
              maxTasks: 2,
              maxDuration: 10000,
              allowFileModification: false,
              allowNetworkAccess: false
            }
          };

          const context2: PlanningContext = {
            userPrompt: 'Second plan',
            projectContext: {},
            availableServices: ['aiService'],
            constraints: {
              maxTasks: 2,
              maxDuration: 10000,
              allowFileModification: false,
              allowNetworkAccess: false
            }
          };

          const plan1 = await taskPlanner.createPlan(context1);
          const plan2 = await taskPlanner.createPlan(context2);

          const activePlans = taskPlanner.getActivePlans();
          
          TestAssertions.assertTrue(activePlans.length >= 2);
          TestAssertions.assertTrue(activePlans.some(p => p.id === plan1.id));
          TestAssertions.assertTrue(activePlans.some(p => p.id === plan2.id));
        }
      },

      {
        name: 'should handle invalid plan IDs gracefully',
        description: 'Test error handling for non-existent plans',
        test: async () => {
          const nonExistentId = 'non-existent-plan-id';
          
          const plan = taskPlanner.getPlan(nonExistentId);
          TestAssertions.assertUndefined(plan);
          
          const cancelled = await taskPlanner.cancelPlan(nonExistentId);
          TestAssertions.assertFalse(cancelled);
        }
      },

      {
        name: 'should validate task constraints',
        description: 'Test constraint validation during planning',
        test: async () => {
          const context: PlanningContext = {
            userPrompt: 'Create a complex multi-step workflow',
            projectContext: {},
            availableServices: ['aiService'],
            constraints: {
              maxTasks: 2, // Very restrictive
              maxDuration: 5000, // Very short
              allowFileModification: false,
              allowNetworkAccess: false
            }
          };

          const plan = await taskPlanner.createPlan(context);
          
          // Plan should respect constraints
          TestAssertions.assertTrue(plan.tasks.length <= context.constraints.maxTasks);
          TestAssertions.assertTrue(plan.totalEstimatedDuration <= context.constraints.maxDuration);
        }
      },

      {
        name: 'should handle AI service failures gracefully',
        description: 'Test fallback behavior when AI service fails',
        test: async () => {
          // Create a registry with a failing AI service
          const failingRegistry = new ServiceRegistry();
          const failingAI = {
            generateCode: async (prompt: string, options: any) => {
              throw new Error('AI service unavailable');
            },
            analyzeImage: async (imageUrl: string, prompt: string) => {
              throw new Error('AI service unavailable');
            },
            chat: async (message: string, context?: any) => {
              throw new Error('AI service unavailable');
            }
          };

          failingRegistry.register('ai', failingAI);
          const failingPlanner = new TaskPlannerService(failingRegistry);

          const context: PlanningContext = {
            userPrompt: 'Test AI failure handling',
            projectContext: {},
            availableServices: ['aiService'],
            constraints: {
              maxTasks: 3,
              maxDuration: 15000,
              allowFileModification: false,
              allowNetworkAccess: false
            }
          };

          // Should still create a plan using fallback logic
          const plan = await failingPlanner.createPlan(context);
          
          TestAssertions.assertNotUndefined(plan);
          TestAssertions.assertTrue(plan.tasks.length > 0);
          TestAssertions.assertEqual(plan.status, 'pending');
        }
      }
    ]
  };
}
