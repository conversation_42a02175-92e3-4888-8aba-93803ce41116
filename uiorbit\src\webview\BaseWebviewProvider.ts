import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from '../utils/Logger';

/**
 * Base class for webview providers with common functionality
 */
export abstract class BaseWebviewProvider implements vscode.WebviewViewProvider {
  protected _view?: vscode.WebviewView;
  protected _extensionUri: vscode.Uri;
  protected _htmlFileName: string;

  constructor(extensionUri: vscode.Uri, htmlFileName: string) {
    this._extensionUri = extensionUri;
    this._htmlFileName = htmlFileName;
  }

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        this._extensionUri
      ]
    };

    webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(
      message => this.handleMessage(message),
      undefined,
      []
    );

    // Initialize the webview
    this.onWebviewReady();
  }

  /**
   * Get the HTML content for the webview
   */
  private _getHtmlForWebview(webview: vscode.Webview): string {
    try {
      const htmlPath = path.join(this._extensionUri.fsPath, 'src', 'webview', 'html', this._htmlFileName);
      let html = fs.readFileSync(htmlPath, 'utf8');

      // Replace relative paths with webview URIs
      html = html.replace(
        /src="([^"]+)"/g,
        (match, src) => {
          if (src.startsWith('http')) {
            return match;
          }
          const resourceUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'src', 'webview', 'assets', src)
          );
          return `src="${resourceUri}"`;
        }
      );

      html = html.replace(
        /href="([^"]+)"/g,
        (match, href) => {
          if (href.startsWith('http') || href.startsWith('#')) {
            return match;
          }
          const resourceUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'src', 'webview', 'assets', href)
          );
          return `href="${resourceUri}"`;
        }
      );

      return html;
    } catch (error) {
      Logger.error(`Failed to load HTML file ${this._htmlFileName}:`, error);
      return this._getErrorHtml();
    }
  }

  /**
   * Get error HTML when the main HTML file fails to load
   */
  private _getErrorHtml(): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Error</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            padding: 20px;
            text-align: center;
          }
          .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
          }
          .error-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
          }
          .error-message {
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
          }
        </style>
      </head>
      <body>
        <div class="error-icon">⚠️</div>
        <div class="error-title">Failed to Load Panel</div>
        <div class="error-message">Could not load the webview content. Please try reloading the extension.</div>
      </body>
      </html>
    `;
  }

  /**
   * Send a message to the webview
   */
  protected postMessage(message: any): void {
    if (this._view) {
      this._view.webview.postMessage(message);
    }
  }

  /**
   * Show the webview (bring it to focus)
   */
  public show(): void {
    if (this._view) {
      this._view.show(true);
    }
  }

  /**
   * Check if the webview is visible
   */
  public get visible(): boolean {
    return this._view?.visible ?? false;
  }

  /**
   * Get the webview instance
   */
  public get webview(): vscode.Webview | undefined {
    return this._view?.webview;
  }

  /**
   * Abstract method to handle messages from the webview
   * Subclasses must implement this method
   */
  protected abstract handleMessage(message: any): void;

  /**
   * Called when the webview is ready
   * Subclasses can override this method for initialization
   */
  protected onWebviewReady(): void {
    // Default implementation does nothing
    Logger.debug(`Webview ${this._htmlFileName} is ready`);
  }

  /**
   * Dispose of the webview
   */
  public dispose(): void {
    if (this._view) {
      this._view = undefined;
    }
  }

  /**
   * Refresh the webview content
   */
  public refresh(): void {
    if (this._view) {
      this._view.webview.html = this._getHtmlForWebview(this._view.webview);
      this.onWebviewReady();
    }
  }

  /**
   * Update the webview with new data
   */
  protected updateWebview(data: any): void {
    this.postMessage({
      type: 'update',
      data: data
    });
  }

  /**
   * Show an error message in the webview
   */
  protected showError(error: string): void {
    this.postMessage({
      type: 'error',
      message: error
    });
  }

  /**
   * Show a success message in the webview
   */
  protected showSuccess(message: string): void {
    this.postMessage({
      type: 'success',
      message: message
    });
  }

  /**
   * Show loading state in the webview
   */
  protected showLoading(message: string = 'Loading...'): void {
    this.postMessage({
      type: 'loading',
      message: message
    });
  }

  /**
   * Hide loading state in the webview
   */
  protected hideLoading(): void {
    this.postMessage({
      type: 'hideLoading'
    });
  }

  /**
   * Get a nonce for CSP
   */
  protected getNonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }

  /**
   * Get webview URI for a local resource
   */
  protected getWebviewUri(webview: vscode.Webview, ...pathSegments: string[]): vscode.Uri {
    return webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, ...pathSegments));
  }
}
