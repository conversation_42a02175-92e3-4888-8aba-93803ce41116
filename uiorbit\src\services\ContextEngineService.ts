import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';
import { ASTAnalysisService, FileAnalysis } from './ASTAnalysisService';
import { VectorDatabaseService, SearchResult, SemanticSearchOptions } from './VectorDatabaseService';
import { WorkspaceAnalysisService } from './WorkspaceAnalysisService';

export interface ContextItem {
  id: string;
  type: 'file' | 'component' | 'function' | 'class' | 'snippet' | 'related';
  content: string;
  filePath: string;
  name?: string;
  relevance: number;
  metadata: {
    language: string;
    framework?: string;
    line?: number;
    size: number;
    lastModified: number;
    dependencies?: string[];
    usages?: string[];
  };
}

export interface ContextRequest {
  query: string;
  currentFile?: string;
  selectedText?: string;
  cursorPosition?: vscode.Position;
  includeTypes?: ('file' | 'component' | 'function' | 'class' | 'snippet' | 'related')[];
  maxItems?: number;
  relevanceThreshold?: number;
}

export interface ContextResponse {
  items: ContextItem[];
  totalRelevance: number;
  processingTime: number;
  suggestions: string[];
}

/**
 * Context Engine Service - Intelligent context aggregation and selection
 * This service provides smart context for AI interactions by analyzing:
 * - Current file and cursor position
 * - Related files and dependencies
 * - Semantic similarity to user queries
 * - Project structure and patterns
 */
export class ContextEngineService {
  private astAnalysisService: ASTAnalysisService;
  private vectorDatabaseService: VectorDatabaseService;
  private workspaceAnalysisService: WorkspaceAnalysisService;
  private contextCache: Map<string, ContextResponse> = new Map();
  private dependencyGraph: Map<string, string[]> = new Map();

  constructor(
    astAnalysisService: ASTAnalysisService,
    vectorDatabaseService: VectorDatabaseService,
    workspaceAnalysisService: WorkspaceAnalysisService
  ) {
    this.astAnalysisService = astAnalysisService;
    this.vectorDatabaseService = vectorDatabaseService;
    this.workspaceAnalysisService = workspaceAnalysisService;
    Logger.info('Context Engine Service initialized');
  }

  /**
   * Get intelligent context for a user request
   */
  async getContext(request: ContextRequest): Promise<ContextResponse> {
    const startTime = Date.now();
    
    try {
      Logger.info(`Getting context for query: "${request.query}"`);

      // Check cache first
      const cacheKey = this.generateCacheKey(request);
      if (this.contextCache.has(cacheKey)) {
        Logger.debug('Returning cached context');
        return this.contextCache.get(cacheKey)!;
      }

      const contextItems: ContextItem[] = [];
      let totalRelevance = 0;

      // 1. Get current file context
      if (request.currentFile) {
        const currentFileContext = await this.getCurrentFileContext(request);
        contextItems.push(...currentFileContext);
      }

      // 2. Get semantic search results
      const semanticResults = await this.getSemanticContext(request);
      contextItems.push(...semanticResults);

      // 3. Get related files context
      const relatedContext = await this.getRelatedFilesContext(request);
      contextItems.push(...relatedContext);

      // 4. Get dependency context
      const dependencyContext = await this.getDependencyContext(request);
      contextItems.push(...dependencyContext);

      // 5. Deduplicate and rank context items
      const rankedItems = this.rankAndDeduplicateContext(contextItems, request);

      // 6. Apply limits and thresholds
      const filteredItems = this.filterContext(rankedItems, request);

      // Calculate total relevance
      totalRelevance = filteredItems.reduce((sum, item) => sum + item.relevance, 0);

      // Generate suggestions
      const suggestions = this.generateSuggestions(filteredItems, request);

      const response: ContextResponse = {
        items: filteredItems,
        totalRelevance,
        processingTime: Date.now() - startTime,
        suggestions
      };

      // Cache the response
      this.contextCache.set(cacheKey, response);

      Logger.info(`Context retrieved: ${filteredItems.length} items, relevance: ${totalRelevance.toFixed(2)}, time: ${response.processingTime}ms`);
      return response;

    } catch (error) {
      Logger.error('Error getting context:', error);
      return {
        items: [],
        totalRelevance: 0,
        processingTime: Date.now() - startTime,
        suggestions: []
      };
    }
  }

  /**
   * Get context from current file
   */
  private async getCurrentFileContext(request: ContextRequest): Promise<ContextItem[]> {
    if (!request.currentFile) return [];

    try {
      const analysis = await this.astAnalysisService.analyzeFile(request.currentFile);
      const contextItems: ContextItem[] = [];

      // Add current file as context
      const fileContent = await this.readFile(request.currentFile);
      contextItems.push({
        id: `current-file:${request.currentFile}`,
        type: 'file',
        content: this.truncateContent(fileContent, 2000),
        filePath: request.currentFile,
        name: request.currentFile.split('/').pop(),
        relevance: 0.9, // High relevance for current file
        metadata: {
          language: analysis.language,
          framework: this.detectFramework(analysis),
          size: fileContent.length,
          lastModified: Date.now()
        }
      });

      // Add components from current file
      for (const component of analysis.components) {
        const componentCode = await this.extractComponentCode(request.currentFile, component);
        contextItems.push({
          id: `current-component:${request.currentFile}:${component.name}`,
          type: 'component',
          content: componentCode,
          filePath: request.currentFile,
          name: component.name,
          relevance: 0.8,
          metadata: {
            language: analysis.language,
            framework: this.detectFramework(analysis),
            line: component.location.line,
            size: componentCode.length,
            lastModified: Date.now()
          }
        });
      }

      // Add functions from current file if relevant to query
      for (const func of analysis.functions) {
        if (this.isRelevantToQuery(func.name || '', request.query)) {
          const functionCode = await this.extractFunctionCode(request.currentFile, func);
          contextItems.push({
            id: `current-function:${request.currentFile}:${func.name}`,
            type: 'function',
            content: functionCode,
            filePath: request.currentFile,
            name: func.name,
            relevance: 0.7,
            metadata: {
              language: analysis.language,
              line: func.line,
              size: functionCode.length,
              lastModified: Date.now()
            }
          });
        }
      }

      return contextItems;

    } catch (error) {
      Logger.error(`Error getting current file context for ${request.currentFile}:`, error);
      return [];
    }
  }

  /**
   * Get semantic context using vector search
   */
  private async getSemanticContext(request: ContextRequest): Promise<ContextItem[]> {
    try {
      const searchOptions: SemanticSearchOptions = {
        query: request.query,
        limit: 10,
        threshold: request.relevanceThreshold || 0.6
      };

      const searchResults = await this.vectorDatabaseService.semanticSearch(searchOptions);
      const contextItems: ContextItem[] = [];

      for (const result of searchResults) {
        contextItems.push({
          id: `semantic:${result.embedding.id}`,
          type: result.embedding.type,
          content: result.embedding.content,
          filePath: result.embedding.filePath,
          name: result.embedding.metadata.name,
          relevance: result.relevance,
          metadata: {
            language: result.embedding.metadata.language,
            framework: result.embedding.metadata.framework,
            line: result.embedding.metadata.line,
            size: result.embedding.metadata.size,
            lastModified: result.embedding.metadata.lastModified
          }
        });
      }

      return contextItems;

    } catch (error) {
      Logger.error('Error getting semantic context:', error);
      return [];
    }
  }

  /**
   * Get context from related files
   */
  private async getRelatedFilesContext(request: ContextRequest): Promise<ContextItem[]> {
    if (!request.currentFile) return [];

    try {
      const relatedFiles = await this.findRelatedFiles(request.currentFile);
      const contextItems: ContextItem[] = [];

      for (const filePath of relatedFiles.slice(0, 5)) { // Limit to 5 related files
        try {
          const analysis = await this.astAnalysisService.analyzeFile(filePath);
          const fileContent = await this.readFile(filePath);

          contextItems.push({
            id: `related-file:${filePath}`,
            type: 'related',
            content: this.truncateContent(fileContent, 1000),
            filePath,
            name: filePath.split('/').pop(),
            relevance: 0.6,
            metadata: {
              language: analysis.language,
              framework: this.detectFramework(analysis),
              size: fileContent.length,
              lastModified: Date.now()
            }
          });

        } catch (error) {
          Logger.warn(`Error analyzing related file ${filePath}:`, error);
        }
      }

      return contextItems;

    } catch (error) {
      Logger.error('Error getting related files context:', error);
      return [];
    }
  }

  /**
   * Get context from dependencies
   */
  private async getDependencyContext(request: ContextRequest): Promise<ContextItem[]> {
    if (!request.currentFile) return [];

    try {
      const dependencies = this.dependencyGraph.get(request.currentFile) || [];
      const contextItems: ContextItem[] = [];

      for (const depPath of dependencies.slice(0, 3)) { // Limit to 3 dependencies
        try {
          const analysis = await this.astAnalysisService.analyzeFile(depPath);
          
          // Add relevant exports from dependency
          for (const exp of analysis.exports) {
            if (this.isRelevantToQuery(exp.name || '', request.query)) {
              const exportCode = await this.extractExportCode(depPath, exp);
              contextItems.push({
                id: `dependency:${depPath}:${exp.name}`,
                type: 'function',
                content: exportCode,
                filePath: depPath,
                name: exp.name,
                relevance: 0.5,
                metadata: {
                  language: analysis.language,
                  line: exp.line,
                  size: exportCode.length,
                  lastModified: Date.now()
                }
              });
            }
          }

        } catch (error) {
          Logger.warn(`Error analyzing dependency ${depPath}:`, error);
        }
      }

      return contextItems;

    } catch (error) {
      Logger.error('Error getting dependency context:', error);
      return [];
    }
  }

  /**
   * Rank and deduplicate context items
   */
  private rankAndDeduplicateContext(items: ContextItem[], request: ContextRequest): ContextItem[] {
    // Remove duplicates based on content similarity
    const uniqueItems = new Map<string, ContextItem>();
    
    for (const item of items) {
      const key = `${item.type}:${item.filePath}:${item.name}`;
      if (!uniqueItems.has(key) || uniqueItems.get(key)!.relevance < item.relevance) {
        uniqueItems.set(key, item);
      }
    }

    // Sort by relevance
    return Array.from(uniqueItems.values()).sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * Filter context based on request parameters
   */
  private filterContext(items: ContextItem[], request: ContextRequest): ContextItem[] {
    let filtered = items;

    // Filter by type if specified
    if (request.includeTypes && request.includeTypes.length > 0) {
      filtered = filtered.filter(item => request.includeTypes!.includes(item.type));
    }

    // Apply relevance threshold
    if (request.relevanceThreshold) {
      filtered = filtered.filter(item => item.relevance >= request.relevanceThreshold!);
    }

    // Apply max items limit
    if (request.maxItems) {
      filtered = filtered.slice(0, request.maxItems);
    }

    return filtered;
  }

  /**
   * Generate suggestions based on context
   */
  private generateSuggestions(items: ContextItem[], request: ContextRequest): string[] {
    const suggestions: string[] = [];

    // Suggest based on available components
    const components = items.filter(item => item.type === 'component');
    if (components.length > 0) {
      suggestions.push(`I found ${components.length} relevant components. Would you like me to help you use them?`);
    }

    // Suggest based on functions
    const functions = items.filter(item => item.type === 'function');
    if (functions.length > 0) {
      suggestions.push(`There are ${functions.length} related functions that might be helpful.`);
    }

    // Suggest based on patterns
    const frameworks = new Set(items.map(item => item.metadata.framework).filter(Boolean));
    if (frameworks.size > 0) {
      suggestions.push(`I can help you with ${Array.from(frameworks).join(', ')} patterns.`);
    }

    return suggestions.slice(0, 3); // Limit to 3 suggestions
  }

  /**
   * Utility methods
   */
  private generateCacheKey(request: ContextRequest): string {
    return `${request.query}:${request.currentFile}:${request.selectedText}:${JSON.stringify(request.includeTypes)}`;
  }

  private async readFile(filePath: string): Promise<string> {
    const uri = vscode.Uri.file(filePath);
    const document = await vscode.workspace.openTextDocument(uri);
    return document.getText();
  }

  private truncateContent(content: string, maxLength: number): string {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  }

  private detectFramework(analysis: FileAnalysis): string {
    const imports = analysis.imports.map(imp => imp.name || '').join(' ');
    if (imports.includes('react')) return 'react';
    if (imports.includes('vue')) return 'vue';
    if (imports.includes('@angular')) return 'angular';
    if (imports.includes('svelte')) return 'svelte';
    return 'vanilla';
  }

  private isRelevantToQuery(name: string, query: string): boolean {
    const lowerName = name.toLowerCase();
    const lowerQuery = query.toLowerCase();
    return lowerName.includes(lowerQuery) || lowerQuery.includes(lowerName);
  }

  private async findRelatedFiles(filePath: string): Promise<string[]> {
    // Implementation would analyze imports, file structure, etc.
    // For now, return empty array
    return [];
  }

  private async extractComponentCode(filePath: string, component: any): Promise<string> {
    const content = await this.readFile(filePath);
    const lines = content.split('\n');
    const startLine = Math.max(0, component.location.line - 1);
    const endLine = Math.min(lines.length, startLine + 50);
    return lines.slice(startLine, endLine).join('\n');
  }

  private async extractFunctionCode(filePath: string, func: any): Promise<string> {
    const content = await this.readFile(filePath);
    const lines = content.split('\n');
    const startLine = Math.max(0, func.line - 1);
    const endLine = Math.min(lines.length, startLine + 30);
    return lines.slice(startLine, endLine).join('\n');
  }

  private async extractExportCode(filePath: string, exp: any): Promise<string> {
    const content = await this.readFile(filePath);
    const lines = content.split('\n');
    const startLine = Math.max(0, exp.line - 1);
    const endLine = Math.min(lines.length, startLine + 20);
    return lines.slice(startLine, endLine).join('\n');
  }

  /**
   * Clear context cache
   */
  clearCache(): void {
    this.contextCache.clear();
    Logger.info('Context cache cleared');
  }

  /**
   * Update dependency graph
   */
  updateDependencyGraph(filePath: string, dependencies: string[]): void {
    this.dependencyGraph.set(filePath, dependencies);
  }
}
