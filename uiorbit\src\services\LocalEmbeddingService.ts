import { Logger } from '../utils/Logger';

/**
 * Local Embedding Service using TF-IDF and semantic hashing
 * Provides privacy-first embeddings without sending code to external APIs
 */
export class LocalEmbeddingService {
  private vocabulary: Map<string, number> = new Map();
  private idfScores: Map<string, number> = new Map();
  private documentCount: number = 0;
  private readonly embeddingDimensions: number = 384; // Smaller than OpenAI's 1536 but sufficient

  constructor() {
    Logger.info('Local Embedding Service initialized');
  }

  /**
   * Generate embedding for text using local TF-IDF + semantic features
   */
  async generateEmbedding(text: string): Promise<number[]> {
    try {
      // Preprocess text
      const tokens = this.tokenize(text);
      const termFreq = this.calculateTermFrequency(tokens);
      
      // Create base TF-IDF vector
      const tfidfVector = this.createTFIDFVector(termFreq);
      
      // Add semantic features
      const semanticFeatures = this.extractSemanticFeatures(text, tokens);
      
      // Combine vectors
      const embedding = this.combineVectors(tfidfVector, semanticFeatures);
      
      // Normalize
      return this.normalizeVector(embedding);

    } catch (error) {
      Logger.error('Error generating local embedding:', error);
      // Return zero vector as fallback
      return new Array(this.embeddingDimensions).fill(0);
    }
  }

  /**
   * Update vocabulary and IDF scores with new document
   */
  updateVocabulary(text: string): void {
    const tokens = this.tokenize(text);
    const uniqueTokens = new Set(tokens);
    
    // Update vocabulary
    for (const token of uniqueTokens) {
      if (!this.vocabulary.has(token)) {
        this.vocabulary.set(token, this.vocabulary.size);
      }
    }
    
    this.documentCount++;
    this.updateIDFScores();
  }

  /**
   * Tokenize text into meaningful tokens
   */
  private tokenize(text: string): string[] {
    // Convert to lowercase and split on non-alphanumeric characters
    const basicTokens = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 1);

    // Add code-specific tokens
    const codeTokens = this.extractCodeTokens(text);
    
    return [...basicTokens, ...codeTokens];
  }

  /**
   * Extract code-specific tokens (camelCase, function names, etc.)
   */
  private extractCodeTokens(text: string): string[] {
    const tokens: string[] = [];
    
    // Extract camelCase words
    const camelCaseMatches = text.match(/[a-z]+[A-Z][a-zA-Z]*/g) || [];
    tokens.push(...camelCaseMatches.map(match => match.toLowerCase()));
    
    // Extract function/method calls
    const functionMatches = text.match(/\w+(?=\s*\()/g) || [];
    tokens.push(...functionMatches.map(match => `func_${match.toLowerCase()}`));
    
    // Extract JSX/HTML tags
    const tagMatches = text.match(/<\/?(\w+)/g) || [];
    tokens.push(...tagMatches.map(match => `tag_${match.replace(/[</>]/g, '').toLowerCase()}`));
    
    // Extract CSS classes
    const classMatches = text.match(/className\s*=\s*["']([^"']+)["']/g) || [];
    tokens.push(...classMatches.map(match => `class_${match.split(/["']/)[1]}`));
    
    return tokens;
  }

  /**
   * Calculate term frequency for tokens
   */
  private calculateTermFrequency(tokens: string[]): Map<string, number> {
    const termFreq = new Map<string, number>();
    
    for (const token of tokens) {
      termFreq.set(token, (termFreq.get(token) || 0) + 1);
    }
    
    // Normalize by document length
    const totalTokens = tokens.length;
    for (const [term, freq] of termFreq) {
      termFreq.set(term, freq / totalTokens);
    }
    
    return termFreq;
  }

  /**
   * Create TF-IDF vector
   */
  private createTFIDFVector(termFreq: Map<string, number>): number[] {
    const vector = new Array(Math.min(this.vocabulary.size, 256)).fill(0);
    
    for (const [term, tf] of termFreq) {
      const vocabIndex = this.vocabulary.get(term);
      if (vocabIndex !== undefined && vocabIndex < vector.length) {
        const idf = this.idfScores.get(term) || 0;
        vector[vocabIndex] = tf * idf;
      }
    }
    
    return vector;
  }

  /**
   * Extract semantic features specific to code
   */
  private extractSemanticFeatures(text: string, tokens: string[]): number[] {
    const features = new Array(128).fill(0);
    
    // Language indicators
    features[0] = text.includes('import') ? 1 : 0;
    features[1] = text.includes('export') ? 1 : 0;
    features[2] = text.includes('function') ? 1 : 0;
    features[3] = text.includes('const') ? 1 : 0;
    features[4] = text.includes('let') ? 1 : 0;
    features[5] = text.includes('var') ? 1 : 0;
    
    // Framework indicators
    features[10] = text.includes('React') || text.includes('jsx') ? 1 : 0;
    features[11] = text.includes('Vue') ? 1 : 0;
    features[12] = text.includes('Angular') ? 1 : 0;
    features[13] = text.includes('useState') || text.includes('useEffect') ? 1 : 0;
    
    // UI/Component indicators
    features[20] = text.includes('component') || text.includes('Component') ? 1 : 0;
    features[21] = text.includes('props') || text.includes('Props') ? 1 : 0;
    features[22] = text.includes('className') ? 1 : 0;
    features[23] = text.includes('style') || text.includes('Style') ? 1 : 0;
    
    // CSS/Styling indicators
    features[30] = text.includes('tailwind') || text.includes('tw-') ? 1 : 0;
    features[31] = text.includes('styled') ? 1 : 0;
    features[32] = text.includes('css') || text.includes('CSS') ? 1 : 0;
    
    // File type indicators
    features[40] = text.includes('.tsx') || text.includes('.jsx') ? 1 : 0;
    features[41] = text.includes('.ts') || text.includes('.js') ? 1 : 0;
    features[42] = text.includes('.css') || text.includes('.scss') ? 1 : 0;
    features[43] = text.includes('.json') ? 1 : 0;
    
    // Complexity indicators
    features[50] = Math.min(tokens.length / 100, 1); // Document length
    features[51] = Math.min((text.match(/\{/g) || []).length / 20, 1); // Nesting level
    features[52] = Math.min((text.match(/\(/g) || []).length / 10, 1); // Function calls
    
    return features;
  }

  /**
   * Combine TF-IDF and semantic vectors
   */
  private combineVectors(tfidfVector: number[], semanticFeatures: number[]): number[] {
    const combined = new Array(this.embeddingDimensions).fill(0);
    
    // Copy TF-IDF features (first part)
    const tfidfLength = Math.min(tfidfVector.length, 256);
    for (let i = 0; i < tfidfLength; i++) {
      combined[i] = tfidfVector[i];
    }
    
    // Copy semantic features (second part)
    const semanticLength = Math.min(semanticFeatures.length, 128);
    for (let i = 0; i < semanticLength; i++) {
      combined[256 + i] = semanticFeatures[i];
    }
    
    return combined;
  }

  /**
   * Normalize vector to unit length
   */
  private normalizeVector(vector: number[]): number[] {
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    
    if (magnitude === 0) {
      return vector;
    }
    
    return vector.map(val => val / magnitude);
  }

  /**
   * Update IDF scores for all terms
   */
  private updateIDFScores(): void {
    for (const [term] of this.vocabulary) {
      // Simple IDF calculation (can be improved with actual document frequency)
      const idf = Math.log(this.documentCount / (1 + this.getTermDocumentFrequency(term)));
      this.idfScores.set(term, idf);
    }
  }

  /**
   * Get document frequency for a term (simplified)
   */
  private getTermDocumentFrequency(term: string): number {
    // For now, assume each term appears in 10% of documents
    // In a full implementation, this would track actual document frequencies
    return Math.max(1, Math.floor(this.documentCount * 0.1));
  }

  /**
   * Get embedding dimensions
   */
  getEmbeddingDimensions(): number {
    return this.embeddingDimensions;
  }

  /**
   * Get vocabulary size
   */
  getVocabularySize(): number {
    return this.vocabulary.size;
  }

  /**
   * Reset the service (for testing)
   */
  reset(): void {
    this.vocabulary.clear();
    this.idfScores.clear();
    this.documentCount = 0;
    Logger.info('Local Embedding Service reset');
  }
}
