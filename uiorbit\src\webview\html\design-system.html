<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design System Analyzer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            padding: 20px;
        }

        .header {
            margin-bottom: 32px;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            color: var(--vscode-foreground);
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
        }

        .analysis-section {
            margin-bottom: 32px;
            padding: 20px;
            background: var(--vscode-sideBar-background);
            border-radius: 8px;
            border: 1px solid var(--vscode-panel-border);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--vscode-foreground);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }

        .color-item {
            text-align: center;
        }

        .color-swatch {
            width: 100%;
            height: 60px;
            border-radius: 8px;
            border: 1px solid var(--vscode-panel-border);
            margin-bottom: 8px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .color-swatch:hover {
            transform: scale(1.05);
        }

        .color-name {
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .color-value {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        .typography-item {
            padding: 16px;
            background: var(--vscode-input-background);
            border-radius: 6px;
            margin-bottom: 12px;
            border: 1px solid var(--vscode-input-border);
        }

        .typography-preview {
            margin-bottom: 8px;
        }

        .typography-details {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .component-card {
            padding: 16px;
            background: var(--vscode-input-background);
            border-radius: 8px;
            border: 1px solid var(--vscode-input-border);
            cursor: pointer;
            transition: border-color 0.2s;
        }

        .component-card:hover {
            border-color: var(--vscode-button-background);
        }

        .component-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .component-path {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin-bottom: 8px;
        }

        .component-props {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 16px;
            background: var(--vscode-input-background);
            border-radius: 6px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--vscode-button-background);
        }

        .stat-label {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-top: 4px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 16px;
        }

        .action-button {
            padding: 8px 16px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
        }

        .action-button:hover {
            background: var(--vscode-button-hoverBackground);
        }

        .action-button.secondary {
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .action-button.secondary:hover {
            background: var(--vscode-button-secondaryHoverBackground);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: var(--vscode-descriptionForeground);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--vscode-descriptionForeground);
            border-radius: 50%;
            border-top-color: var(--vscode-button-background);
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: var(--vscode-descriptionForeground);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--vscode-foreground);
        }

        .empty-description {
            font-size: 14px;
            margin-bottom: 24px;
        }

        .tag {
            display: inline-block;
            padding: 2px 6px;
            background: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            border-radius: 3px;
            font-size: 10px;
            font-weight: 500;
            margin-right: 4px;
            margin-bottom: 4px;
        }

        .consistency-score {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
        }

        .score-bar {
            flex: 1;
            height: 6px;
            background: var(--vscode-progressBar-background);
            border-radius: 3px;
            overflow: hidden;
        }

        .score-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .score-excellent { background: #4CAF50; }
        .score-good { background: #8BC34A; }
        .score-fair { background: #FFC107; }
        .score-poor { background: #FF5722; }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">🎨 Design System Analyzer</div>
        <div class="subtitle">Analyze and understand your project's design patterns</div>
    </div>

    <div id="loadingState" class="loading">
        <div class="loading-spinner"></div>
        Analyzing design system...
    </div>

    <div id="analysisContent" style="display: none;">
        <!-- Overview Stats -->
        <div class="analysis-section">
            <div class="section-title">📊 Overview</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="componentCount">0</div>
                    <div class="stat-label">Components</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="colorCount">0</div>
                    <div class="stat-label">Colors</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="fontCount">0</div>
                    <div class="stat-label">Font Families</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="consistencyScore">0%</div>
                    <div class="stat-label">Consistency</div>
                </div>
            </div>
            <div class="consistency-score">
                <span>Design Consistency:</span>
                <div class="score-bar">
                    <div class="score-fill" id="consistencyBar" style="width: 0%"></div>
                </div>
                <span id="consistencyLabel">Analyzing...</span>
            </div>
        </div>

        <!-- Color Palette -->
        <div class="analysis-section">
            <div class="section-title">🎨 Color Palette</div>
            <div class="color-palette" id="colorPalette">
                <!-- Colors will be populated here -->
            </div>
            <div class="action-buttons">
                <button class="action-button" onclick="exportColors()">Export Palette</button>
                <button class="action-button secondary" onclick="generateColorVariants()">Generate Variants</button>
            </div>
        </div>

        <!-- Typography -->
        <div class="analysis-section">
            <div class="section-title">📝 Typography</div>
            <div id="typographyList">
                <!-- Typography items will be populated here -->
            </div>
            <div class="action-buttons">
                <button class="action-button" onclick="exportTypography()">Export Typography</button>
                <button class="action-button secondary" onclick="suggestImprovements()">Suggest Improvements</button>
            </div>
        </div>

        <!-- Components -->
        <div class="analysis-section">
            <div class="section-title">🧩 Components</div>
            <div class="component-grid" id="componentGrid">
                <!-- Components will be populated here -->
            </div>
            <div class="action-buttons">
                <button class="action-button" onclick="generateDocumentation()">Generate Docs</button>
                <button class="action-button secondary" onclick="analyzeUsage()">Analyze Usage</button>
            </div>
        </div>
    </div>

    <div id="emptyState" class="empty-state" style="display: none;">
        <div class="empty-icon">🎨</div>
        <div class="empty-title">No Design System Found</div>
        <div class="empty-description">
            We couldn't find any components or design patterns in your project.
            Start by creating some components to see the analysis.
        </div>
        <button class="action-button" onclick="createSampleComponents()">Create Sample Components</button>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        // Initialize analysis on load
        window.addEventListener('load', () => {
            analyzeDesignSystem();
        });

        // Listen for messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.type) {
                case 'analysisComplete':
                    displayAnalysis(message.data);
                    break;
                case 'analysisError':
                    showError(message.error);
                    break;
            }
        });

        function analyzeDesignSystem() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('analysisContent').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';
            
            vscode.postMessage({ type: 'analyzeDesignSystem' });
        }

        function displayAnalysis(data) {
            document.getElementById('loadingState').style.display = 'none';
            
            if (!data || data.components.length === 0) {
                document.getElementById('emptyState').style.display = 'block';
                return;
            }

            document.getElementById('analysisContent').style.display = 'block';
            
            // Update stats
            document.getElementById('componentCount').textContent = data.components.length;
            document.getElementById('colorCount').textContent = data.colors.length;
            document.getElementById('fontCount').textContent = data.typography.length;
            document.getElementById('consistencyScore').textContent = Math.round(data.consistencyScore) + '%';
            
            // Update consistency bar
            const consistencyBar = document.getElementById('consistencyBar');
            const consistencyLabel = document.getElementById('consistencyLabel');
            const score = data.consistencyScore;
            
            consistencyBar.style.width = score + '%';
            
            if (score >= 80) {
                consistencyBar.className = 'score-fill score-excellent';
                consistencyLabel.textContent = 'Excellent';
            } else if (score >= 60) {
                consistencyBar.className = 'score-fill score-good';
                consistencyLabel.textContent = 'Good';
            } else if (score >= 40) {
                consistencyBar.className = 'score-fill score-fair';
                consistencyLabel.textContent = 'Fair';
            } else {
                consistencyBar.className = 'score-fill score-poor';
                consistencyLabel.textContent = 'Needs Improvement';
            }
            
            // Populate colors
            populateColors(data.colors);
            
            // Populate typography
            populateTypography(data.typography);
            
            // Populate components
            populateComponents(data.components);
        }

        function populateColors(colors) {
            const container = document.getElementById('colorPalette');
            container.innerHTML = '';
            
            colors.forEach(color => {
                const colorItem = document.createElement('div');
                colorItem.className = 'color-item';
                colorItem.innerHTML = `
                    <div class="color-swatch" style="background-color: ${color.value}" onclick="copyToClipboard('${color.value}')"></div>
                    <div class="color-name">${color.name}</div>
                    <div class="color-value">${color.value}</div>
                `;
                container.appendChild(colorItem);
            });
        }

        function populateTypography(typography) {
            const container = document.getElementById('typographyList');
            container.innerHTML = '';
            
            typography.forEach(font => {
                const fontItem = document.createElement('div');
                fontItem.className = 'typography-item';
                fontItem.innerHTML = `
                    <div class="typography-preview" style="font-family: ${font.family}; font-size: ${font.size}; font-weight: ${font.weight};">
                        ${font.sample || 'The quick brown fox jumps over the lazy dog'}
                    </div>
                    <div class="typography-details">
                        ${font.family} • ${font.size} • ${font.weight}
                    </div>
                `;
                container.appendChild(fontItem);
            });
        }

        function populateComponents(components) {
            const container = document.getElementById('componentGrid');
            container.innerHTML = '';
            
            components.forEach(component => {
                const componentCard = document.createElement('div');
                componentCard.className = 'component-card';
                componentCard.onclick = () => openComponent(component.path);
                
                const tags = component.tags ? component.tags.map(tag => `<span class="tag">${tag}</span>`).join('') : '';
                
                componentCard.innerHTML = `
                    <div class="component-name">${component.name}</div>
                    <div class="component-path">${component.path}</div>
                    <div class="component-props">${component.props || 'No props'}</div>
                    <div style="margin-top: 8px;">${tags}</div>
                `;
                container.appendChild(componentCard);
            });
        }

        function showError(error) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('emptyState').style.display = 'block';
            document.querySelector('.empty-title').textContent = 'Analysis Error';
            document.querySelector('.empty-description').textContent = error;
        }

        function copyToClipboard(text) {
            vscode.postMessage({ type: 'copyToClipboard', text });
        }

        function openComponent(path) {
            vscode.postMessage({ type: 'openFile', path });
        }

        function exportColors() {
            vscode.postMessage({ type: 'exportColors' });
        }

        function generateColorVariants() {
            vscode.postMessage({ type: 'generateColorVariants' });
        }

        function exportTypography() {
            vscode.postMessage({ type: 'exportTypography' });
        }

        function suggestImprovements() {
            vscode.postMessage({ type: 'suggestImprovements' });
        }

        function generateDocumentation() {
            vscode.postMessage({ type: 'generateDocumentation' });
        }

        function analyzeUsage() {
            vscode.postMessage({ type: 'analyzeUsage' });
        }

        function createSampleComponents() {
            vscode.postMessage({ type: 'createSampleComponents' });
        }
    </script>
</body>
</html>
