import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';
import { ServiceRegistry } from '../core/ServiceRegistry';
import { AIService } from './AIService';

export interface Task {
  id: string;
  name: string;
  description: string;
  type: 'analysis' | 'generation' | 'modification' | 'validation' | 'file-operation';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: number;
  dependencies: string[];
  inputs: { [key: string]: any };
  outputs: { [key: string]: any };
  metadata: {
    estimatedDuration: number; // in milliseconds
    actualDuration?: number;
    startTime?: Date;
    endTime?: Date;
    retryCount: number;
    maxRetries: number;
  };
  onProgress?: (progress: TaskProgress) => void;
  onComplete?: (result: TaskResult) => void;
  onError?: (error: TaskError) => void;
}

export interface TaskProgress {
  taskId: string;
  progress: number; // 0-100
  message: string;
  currentStep?: string;
  totalSteps?: number;
  currentStepIndex?: number;
}

export interface TaskResult {
  taskId: string;
  success: boolean;
  data: any;
  message: string;
  duration: number;
}

export interface TaskError {
  taskId: string;
  error: Error;
  retryable: boolean;
  context: any;
}

export interface TaskPlan {
  id: string;
  name: string;
  description: string;
  tasks: Task[];
  totalEstimatedDuration: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  progress: {
    completedTasks: number;
    totalTasks: number;
    overallProgress: number;
    currentTask?: string;
  };
}

export interface PlanningContext {
  userPrompt: string;
  projectContext: any;
  availableServices: string[];
  constraints: {
    maxTasks: number;
    maxDuration: number;
    allowFileModification: boolean;
    allowNetworkAccess: boolean;
  };
}

export interface TaskTemplate {
  type: string;
  name: string;
  description: string;
  inputs: { [key: string]: any };
  estimatedDuration: number;
  dependencies: string[];
  serviceRequired: string;
}

export class TaskPlannerService {
  private aiService: AIService;
  private activePlans: Map<string, TaskPlan> = new Map();
  private taskTemplates: Map<string, TaskTemplate> = new Map();

  constructor(private serviceRegistry: ServiceRegistry) {
    this.aiService = serviceRegistry.get<AIService>('ai')!;
    this.initializeTaskTemplates();
  }

  /**
   * Create a task plan from user prompt
   */
  async createPlan(context: PlanningContext): Promise<TaskPlan> {
    Logger.info(`Creating task plan for: "${context.userPrompt}"`);

    try {
      // 1. Analyze the user prompt to understand intent
      const intent = await this.analyzeUserIntent(context.userPrompt);

      // 2. Generate task breakdown using AI
      const taskBreakdown = await this.generateTaskBreakdown(context, intent);

      // 3. Create detailed task plan
      const plan = await this.createDetailedPlan(taskBreakdown, context);

      // 4. Validate and optimize the plan
      const optimizedPlan = await this.optimizePlan(plan);

      // 5. Store the plan
      this.activePlans.set(optimizedPlan.id, optimizedPlan);

      Logger.info(`Task plan created with ${optimizedPlan.tasks.length} tasks`);
      return optimizedPlan;

    } catch (error) {
      Logger.error('Failed to create task plan:', error);
      throw new Error(`Task planning failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get plan by ID
   */
  getPlan(planId: string): TaskPlan | undefined {
    return this.activePlans.get(planId);
  }

  /**
   * Get all active plans
   */
  getActivePlans(): TaskPlan[] {
    return Array.from(this.activePlans.values());
  }

  /**
   * Cancel a plan
   */
  async cancelPlan(planId: string): Promise<boolean> {
    const plan = this.activePlans.get(planId);
    if (!plan) {
      return false;
    }

    plan.status = 'cancelled';
    plan.tasks.forEach(task => {
      if (task.status === 'pending' || task.status === 'running') {
        task.status = 'cancelled';
      }
    });

    Logger.info(`Task plan cancelled: ${planId}`);
    return true;
  }

  /**
   * Update plan progress
   */
  updatePlanProgress(planId: string): void {
    const plan = this.activePlans.get(planId);
    if (!plan) return;

    const completedTasks = plan.tasks.filter(t => t.status === 'completed').length;
    const totalTasks = plan.tasks.length;
    const overallProgress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    plan.progress = {
      completedTasks,
      totalTasks,
      overallProgress,
      currentTask: plan.tasks.find(t => t.status === 'running')?.name
    };

    // Update plan status
    if (completedTasks === totalTasks) {
      plan.status = 'completed';
      plan.completedAt = new Date();
    } else if (plan.tasks.some(t => t.status === 'failed')) {
      plan.status = 'failed';
    } else if (plan.tasks.some(t => t.status === 'running')) {
      plan.status = 'running';
      if (!plan.startedAt) {
        plan.startedAt = new Date();
      }
    }
  }

  private async analyzeUserIntent(prompt: string): Promise<any> {
    const analysisPrompt = `Analyze this user request and identify the main intent and required actions:

"${prompt}"

Return a JSON object with:
{
  "intent": "component-generation" | "code-analysis" | "refactoring" | "testing" | "documentation" | "project-setup",
  "complexity": "simple" | "medium" | "complex",
  "requiredServices": ["service1", "service2"],
  "estimatedTasks": number,
  "mainActions": ["action1", "action2"],
  "outputType": "component" | "analysis" | "refactored-code" | "tests" | "documentation"
}`;

    try {
      const response = await this.aiService.generateCode(analysisPrompt, {
        language: 'json',
        maxTokens: 500,
        temperature: 0.1
      });

      return JSON.parse(response);
    } catch (error) {
      Logger.warn('Failed to analyze user intent with AI, using fallback');
      return {
        intent: 'component-generation',
        complexity: 'medium',
        requiredServices: ['aiService'],
        estimatedTasks: 3,
        mainActions: ['analyze', 'generate', 'validate'],
        outputType: 'component'
      };
    }
  }

  private async generateTaskBreakdown(context: PlanningContext, intent: any): Promise<any> {
    const breakdownPrompt = `Break down this user request into specific, actionable tasks:

User Request: "${context.userPrompt}"
Intent Analysis: ${JSON.stringify(intent)}
Available Services: ${context.availableServices.join(', ')}

Create a task breakdown with:
1. Task sequence (what needs to be done in order)
2. Dependencies between tasks
3. Estimated duration for each task
4. Required inputs and expected outputs
5. Service requirements

Return a JSON array of tasks with this structure:
[
  {
    "name": "Task Name",
    "description": "Detailed description",
    "type": "analysis|generation|modification|validation|file-operation",
    "dependencies": ["task1", "task2"],
    "estimatedDuration": 5000,
    "inputs": {"key": "value"},
    "serviceRequired": "serviceName"
  }
]`;

    try {
      const response = await this.aiService.generateCode(breakdownPrompt, {
        language: 'json',
        maxTokens: 1500,
        temperature: 0.2
      });

      return JSON.parse(response);
    } catch (error) {
      Logger.warn('Failed to generate task breakdown with AI, using template');
      return this.getDefaultTaskBreakdown(intent);
    }
  }

  private async createDetailedPlan(taskBreakdown: any[], context: PlanningContext): Promise<TaskPlan> {
    const planId = this.generateId();
    const tasks: Task[] = [];

    taskBreakdown.forEach((taskData, index) => {
      const task: Task = {
        id: this.generateId(),
        name: taskData.name,
        description: taskData.description,
        type: taskData.type,
        status: 'pending',
        priority: index + 1,
        dependencies: taskData.dependencies || [],
        inputs: taskData.inputs || {},
        outputs: {},
        metadata: {
          estimatedDuration: taskData.estimatedDuration || 5000,
          retryCount: 0,
          maxRetries: 3
        }
      };

      tasks.push(task);
    });

    const totalEstimatedDuration = tasks.reduce((sum, task) => sum + task.metadata.estimatedDuration, 0);

    const plan: TaskPlan = {
      id: planId,
      name: `Plan for: ${context.userPrompt.substring(0, 50)}...`,
      description: context.userPrompt,
      tasks,
      totalEstimatedDuration,
      status: 'pending',
      createdAt: new Date(),
      progress: {
        completedTasks: 0,
        totalTasks: tasks.length,
        overallProgress: 0
      }
    };

    return plan;
  }

  private async optimizePlan(plan: TaskPlan): Promise<TaskPlan> {
    // Optimize task order based on dependencies
    const optimizedTasks = this.topologicalSort(plan.tasks);
    
    // Update priorities based on optimized order
    optimizedTasks.forEach((task, index) => {
      task.priority = index + 1;
    });

    return {
      ...plan,
      tasks: optimizedTasks
    };
  }

  private topologicalSort(tasks: Task[]): Task[] {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const result: Task[] = [];
    const taskMap = new Map(tasks.map(t => [t.name, t]));

    const visit = (taskName: string) => {
      if (visiting.has(taskName)) {
        throw new Error(`Circular dependency detected involving task: ${taskName}`);
      }
      if (visited.has(taskName)) {
        return;
      }

      visiting.add(taskName);
      const task = taskMap.get(taskName);
      if (task) {
        task.dependencies.forEach(dep => visit(dep));
        visiting.delete(taskName);
        visited.add(taskName);
        result.push(task);
      }
    };

    tasks.forEach(task => {
      if (!visited.has(task.name)) {
        visit(task.name);
      }
    });

    return result;
  }

  private getDefaultTaskBreakdown(intent: any): any[] {
    const templates = {
      'component-generation': [
        {
          name: 'Analyze Requirements',
          description: 'Analyze user requirements and project context',
          type: 'analysis',
          dependencies: [],
          estimatedDuration: 3000,
          serviceRequired: 'contextEngine'
        },
        {
          name: 'Generate Component',
          description: 'Generate component code based on requirements',
          type: 'generation',
          dependencies: ['Analyze Requirements'],
          estimatedDuration: 8000,
          serviceRequired: 'aiService'
        },
        {
          name: 'Validate Output',
          description: 'Validate generated code for syntax and best practices',
          type: 'validation',
          dependencies: ['Generate Component'],
          estimatedDuration: 2000,
          serviceRequired: 'astAnalysis'
        }
      ]
    };

    return templates[intent.intent as keyof typeof templates] || templates['component-generation'];
  }

  private initializeTaskTemplates(): void {
    // Initialize common task templates
    const templates: TaskTemplate[] = [
      {
        type: 'analysis',
        name: 'Code Analysis',
        description: 'Analyze existing code structure and patterns',
        inputs: { filePaths: 'string[]' },
        estimatedDuration: 5000,
        dependencies: [],
        serviceRequired: 'astAnalysis'
      },
      {
        type: 'generation',
        name: 'Component Generation',
        description: 'Generate new component code',
        inputs: { specification: 'object', framework: 'string' },
        estimatedDuration: 10000,
        dependencies: [],
        serviceRequired: 'aiService'
      },
      {
        type: 'file-operation',
        name: 'File Creation',
        description: 'Create new files in the workspace',
        inputs: { content: 'string', path: 'string' },
        estimatedDuration: 1000,
        dependencies: [],
        serviceRequired: 'fileOperations'
      }
    ];

    templates.forEach(template => {
      this.taskTemplates.set(template.type, template);
    });
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
}
