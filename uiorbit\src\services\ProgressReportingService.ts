import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';
import { ServiceRegistry } from '../core/ServiceRegistry';

export interface ProgressStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  progress: number; // 0-100
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  error?: string;
  metadata?: { [key: string]: any };
}

export interface ProgressSession {
  id: string;
  title: string;
  description: string;
  steps: ProgressStep[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  overallProgress: number;
  startTime: Date;
  endTime?: Date;
  totalDuration?: number;
  cancellable: boolean;
  showInStatusBar: boolean;
  showInNotification: boolean;
  showInWebview: boolean;
}

export interface ProgressOptions {
  title: string;
  description?: string;
  cancellable?: boolean;
  showInStatusBar?: boolean;
  showInNotification?: boolean;
  showInWebview?: boolean;
  location?: vscode.ProgressLocation;
}

export interface ProgressUpdate {
  sessionId: string;
  stepId?: string;
  progress?: number;
  message?: string;
  status?: 'running' | 'completed' | 'failed';
  increment?: number;
}

export class ProgressReportingService {
  private activeSessions: Map<string, ProgressSession> = new Map();
  private vscodeProgress: Map<string, vscode.Progress<{ message?: string; increment?: number }>> = new Map();
  private statusBarItems: Map<string, vscode.StatusBarItem> = new Map();
  private webviewProgressCallbacks: Map<string, (update: ProgressUpdate) => void> = new Map();

  constructor(private serviceRegistry: ServiceRegistry) {
    this.initializeService();
  }

  /**
   * Start a new progress session
   */
  async startProgress(options: ProgressOptions, steps?: ProgressStep[]): Promise<string> {
    const sessionId = this.generateId();
    
    const session: ProgressSession = {
      id: sessionId,
      title: options.title,
      description: options.description || '',
      steps: steps || [],
      status: 'pending',
      overallProgress: 0,
      startTime: new Date(),
      cancellable: options.cancellable || false,
      showInStatusBar: options.showInStatusBar || false,
      showInNotification: options.showInNotification || true,
      showInWebview: options.showInWebview || true
    };

    this.activeSessions.set(sessionId, session);

    // Initialize VS Code progress
    if (session.showInNotification) {
      await this.initializeVSCodeProgress(session, options.location);
    }

    // Initialize status bar
    if (session.showInStatusBar) {
      this.initializeStatusBar(session);
    }

    // Initialize webview progress
    if (session.showInWebview) {
      this.initializeWebviewProgress(session);
    }

    Logger.info(`Progress session started: ${options.title}`);
    return sessionId;
  }

  /**
   * Update progress for a session or specific step
   */
  async updateProgress(update: ProgressUpdate): Promise<void> {
    const session = this.activeSessions.get(update.sessionId);
    if (!session) {
      Logger.warn(`Progress session not found: ${update.sessionId}`);
      return;
    }

    // Update specific step
    if (update.stepId) {
      const step = session.steps.find(s => s.id === update.stepId);
      if (step) {
        if (update.progress !== undefined) {
          step.progress = update.progress;
        }
        if (update.status) {
          step.status = update.status;
          if (update.status === 'running' && !step.startTime) {
            step.startTime = new Date();
          }
          if ((update.status === 'completed' || update.status === 'failed') && step.startTime && !step.endTime) {
            step.endTime = new Date();
            step.duration = step.endTime.getTime() - step.startTime.getTime();
          }
        }
      }
    }

    // Update overall session progress
    if (update.progress !== undefined) {
      session.overallProgress = update.progress;
    } else if (session.steps.length > 0) {
      // Calculate overall progress from steps
      const totalProgress = session.steps.reduce((sum, step) => sum + step.progress, 0);
      session.overallProgress = totalProgress / session.steps.length;
    }

    // Update session status
    if (update.status) {
      session.status = update.status;
      if (update.status === 'completed' || update.status === 'failed') {
        session.endTime = new Date();
        session.totalDuration = session.endTime.getTime() - session.startTime.getTime();
      }
    }

    // Update all progress indicators
    await this.updateAllIndicators(session, update);
  }

  /**
   * Add a step to an existing session
   */
  addStep(sessionId: string, step: ProgressStep): void {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.steps.push(step);
      this.notifyWebviewUpdate(sessionId, { sessionId, message: `Added step: ${step.name}` });
    }
  }

  /**
   * Complete a progress session
   */
  async completeProgress(sessionId: string, success: boolean = true): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return;
    }

    session.status = success ? 'completed' : 'failed';
    session.endTime = new Date();
    session.totalDuration = session.endTime.getTime() - session.startTime.getTime();
    session.overallProgress = 100;

    await this.updateAllIndicators(session, {
      sessionId,
      progress: 100,
      status: success ? 'completed' : 'failed',
      message: success ? 'Completed successfully' : 'Failed'
    });

    // Clean up after a delay
    setTimeout(() => {
      this.cleanupSession(sessionId);
    }, 5000);

    Logger.info(`Progress session ${success ? 'completed' : 'failed'}: ${session.title}`);
  }

  /**
   * Cancel a progress session
   */
  async cancelProgress(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.cancellable) {
      return;
    }

    session.status = 'cancelled';
    session.endTime = new Date();
    session.totalDuration = session.endTime.getTime() - session.startTime.getTime();

    await this.updateAllIndicators(session, {
      sessionId,
      status: 'failed',
      message: 'Cancelled by user'
    });

    this.cleanupSession(sessionId);
    Logger.info(`Progress session cancelled: ${session.title}`);
  }

  /**
   * Get active sessions
   */
  getActiveSessions(): ProgressSession[] {
    return Array.from(this.activeSessions.values());
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): ProgressSession | undefined {
    return this.activeSessions.get(sessionId);
  }

  /**
   * Register webview progress callback
   */
  registerWebviewCallback(sessionId: string, callback: (update: ProgressUpdate) => void): void {
    this.webviewProgressCallbacks.set(sessionId, callback);
  }

  /**
   * Unregister webview progress callback
   */
  unregisterWebviewCallback(sessionId: string): void {
    this.webviewProgressCallbacks.delete(sessionId);
  }

  private async initializeVSCodeProgress(session: ProgressSession, location?: vscode.ProgressLocation): Promise<void> {
    const progressLocation = location || vscode.ProgressLocation.Notification;

    vscode.window.withProgress(
      {
        location: progressLocation,
        title: session.title,
        cancellable: session.cancellable
      },
      async (progress, token) => {
        // Store progress reporter
        this.vscodeProgress.set(session.id, progress);

        // Handle cancellation
        if (session.cancellable) {
          token.onCancellationRequested(() => {
            this.cancelProgress(session.id);
          });
        }

        // Wait for session completion
        return new Promise<void>((resolve) => {
          const checkCompletion = () => {
            const currentSession = this.activeSessions.get(session.id);
            if (!currentSession || currentSession.status === 'completed' || currentSession.status === 'failed' || currentSession.status === 'cancelled') {
              resolve();
            } else {
              setTimeout(checkCompletion, 100);
            }
          };
          checkCompletion();
        });
      }
    );
  }

  private initializeStatusBar(session: ProgressSession): void {
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    statusBarItem.text = `$(sync~spin) ${session.title}`;
    statusBarItem.tooltip = session.description;
    statusBarItem.show();

    this.statusBarItems.set(session.id, statusBarItem);
  }

  private initializeWebviewProgress(session: ProgressSession): void {
    // This would integrate with the webview to show progress
    this.notifyWebviewUpdate(session.id, {
      sessionId: session.id,
      message: `Started: ${session.title}`
    });
  }

  private async updateAllIndicators(session: ProgressSession, update: ProgressUpdate): Promise<void> {
    // Update VS Code progress
    const vscodeProgress = this.vscodeProgress.get(session.id);
    if (vscodeProgress) {
      vscodeProgress.report({
        message: update.message,
        increment: update.increment
      });
    }

    // Update status bar
    const statusBarItem = this.statusBarItems.get(session.id);
    if (statusBarItem) {
      const icon = this.getStatusIcon(session.status);
      statusBarItem.text = `${icon} ${session.title} (${Math.round(session.overallProgress)}%)`;
      statusBarItem.tooltip = update.message || session.description;
    }

    // Update webview
    this.notifyWebviewUpdate(session.id, update);
  }

  private notifyWebviewUpdate(sessionId: string, update: ProgressUpdate): void {
    const callback = this.webviewProgressCallbacks.get(sessionId);
    if (callback) {
      callback(update);
    }

    // Also emit to any global webview listeners
    // This would integrate with the chat webview to show progress
  }

  private getStatusIcon(status: string): string {
    switch (status) {
      case 'pending': return '$(clock)';
      case 'running': return '$(sync~spin)';
      case 'completed': return '$(check)';
      case 'failed': return '$(error)';
      case 'cancelled': return '$(close)';
      default: return '$(question)';
    }
  }

  private cleanupSession(sessionId: string): void {
    // Remove from active sessions
    this.activeSessions.delete(sessionId);

    // Clean up VS Code progress
    this.vscodeProgress.delete(sessionId);

    // Clean up status bar
    const statusBarItem = this.statusBarItems.get(sessionId);
    if (statusBarItem) {
      statusBarItem.dispose();
      this.statusBarItems.delete(sessionId);
    }

    // Clean up webview callback
    this.webviewProgressCallbacks.delete(sessionId);
  }

  private initializeService(): void {
    Logger.info('Progress Reporting Service initialized');
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * Create a progress session with predefined steps
   */
  async createSteppedProgress(
    options: ProgressOptions,
    stepNames: string[]
  ): Promise<string> {
    const steps: ProgressStep[] = stepNames.map((name, index) => ({
      id: this.generateId(),
      name,
      description: name,
      status: 'pending',
      progress: 0
    }));

    return await this.startProgress(options, steps);
  }

  /**
   * Update step status and auto-advance
   */
  async updateStep(
    sessionId: string,
    stepIndex: number,
    status: 'completed' | 'failed',
    message?: string
  ): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.steps[stepIndex]) {
      return;
    }

    const step = session.steps[stepIndex];
    await this.updateProgress({
      sessionId,
      stepId: step.id,
      progress: status === 'completed' ? 100 : step.progress,
      status,
      message: message || (status === 'completed' ? `${step.name} completed` : `${step.name} failed`)
    });

    // Auto-start next step if current completed
    if (status === 'completed' && stepIndex + 1 < session.steps.length) {
      const nextStep = session.steps[stepIndex + 1];
      await this.updateProgress({
        sessionId,
        stepId: nextStep.id,
        status: 'running',
        message: `Starting ${nextStep.name}...`
      });
    }
  }
}
