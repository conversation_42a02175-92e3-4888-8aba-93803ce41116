import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from '../utils/Logger';

/**
 * Configuration service for managing UIOrbit settings
 * Handles both VS Code settings and .env file configuration
 */
export class ConfigurationService {
  private config: UIOrbitConfig | undefined;
  private envConfig: Record<string, string> = {};

  /**
   * Initialize the configuration service
   */
  async initialize(): Promise<void> {
    Logger.info('Initializing configuration service...');

    try {
      // Load .env file if it exists
      await this.loadEnvFile();

      // Load VS Code configuration
      await this.loadVSCodeConfig();

      Logger.info('Configuration service initialized successfully');
    } catch (error) {
      Logger.error('Failed to initialize configuration service:', error);
      throw error;
    }
  }

  /**
   * Reload configuration
   */
  async reload(): Promise<void> {
    Logger.info('Reloading configuration...');
    await this.initialize();
  }

  /**
   * Get the current configuration
   */
  getConfig(): UIOrbitConfig {
    if (!this.config) {
      throw new Error('Configuration not initialized. Call initialize() first.');
    }
    return this.config;
  }

  /**
   * Get a configuration value by key
   */
  async getConfiguration(key: string): Promise<string | undefined> {
    // If key already has uiorbit prefix, use it as-is, otherwise add prefix
    const configKey = key.startsWith('uiorbit.') ? key : `uiorbit.${key}`;
    return vscode.workspace.getConfiguration().get<string>(configKey);
  }

  /**
   * Get OpenAI API key
   */
  getOpenAIApiKey(): string {
    const envKey = this.envConfig.OPENAI_API_KEY;
    const vsCodeKey = vscode.workspace.getConfiguration().get<string>('uiorbit.openaiApiKey');

    Logger.debug(`API Key sources - Env: ${envKey ? 'Found' : 'Not found'}, VSCode: ${vsCodeKey ? 'Found' : 'Not found'}`);
    Logger.debug(`Env config keys: ${Object.keys(this.envConfig).join(', ')}`);
    Logger.debug(`Raw env key value: ${envKey ? envKey.substring(0, 10) + '...' : 'undefined'}`);
    Logger.debug(`VSCode key value: ${vsCodeKey ? vsCodeKey.substring(0, 10) + '...' : 'undefined'}`);

    const finalKey = envKey || vsCodeKey || '';
    if (finalKey) {
      Logger.debug(`Using API key from ${envKey ? '.env file' : 'VS Code settings'}`);
    } else {
      Logger.warn('No OpenAI API key found in .env file or VS Code settings');
    }

    return finalKey;
  }

  /**
   * Check if OpenAI API key is configured
   */
  hasOpenAIApiKey(): boolean {
    return this.getOpenAIApiKey().length > 0;
  }

  /**
   * Get default framework
   */
  getDefaultFramework(): string {
    return this.envConfig.DEFAULT_FRAMEWORK ||
           vscode.workspace.getConfiguration().get<string>('uiorbit.defaultFramework') ||
           'react';
  }

  /**
   * Get default styling approach
   */
  getDefaultStyling(): string {
    return this.envConfig.DEFAULT_STYLING ||
           vscode.workspace.getConfiguration().get<string>('uiorbit.defaultStyling') ||
           'tailwind';
  }

  /**
   * Check if debug mode is enabled
   */
  isDebugMode(): boolean {
    const envDebug = this.envConfig.DEBUG_MODE === 'true';
    const vsCodeDebug = vscode.workspace.getConfiguration().get<boolean>('uiorbit.debugMode');

    return envDebug || vsCodeDebug || false;
  }

  /**
   * Check if accessibility features are enabled
   */
  isAccessibilityEnabled(): boolean {
    const envA11y = this.envConfig.ENABLE_ACCESSIBILITY === 'true';
    const vsCodeA11y = vscode.workspace.getConfiguration().get<boolean>('uiorbit.enableAccessibility');

    return envA11y !== false && (vsCodeA11y !== false);
  }

  /**
   * Check if responsive design is enabled
   */
  isResponsiveDesignEnabled(): boolean {
    const envResponsive = this.envConfig.ENABLE_RESPONSIVE_DESIGN === 'true';
    const vsCodeResponsive = vscode.workspace.getConfiguration().get<boolean>('uiorbit.enableResponsiveDesign');

    return envResponsive !== false && (vsCodeResponsive !== false);
  }

  /**
   * Get API rate limit per minute
   */
  getApiRateLimit(): number {
    return parseInt(this.envConfig.API_RATE_LIMIT_PER_MINUTE || '60');
  }

  /**
   * Get maximum concurrent requests
   */
  getMaxConcurrentRequests(): number {
    return parseInt(this.envConfig.MAX_CONCURRENT_REQUESTS || '5');
  }

  /**
   * Load environment variables from .env file
   */
  private async loadEnvFile(): Promise<void> {
    try {
      // First try to load from extension directory (uiorbit/.env)
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (!workspaceFolder) {
        Logger.debug('No workspace folder found, skipping .env file loading');
        return;
      }

      // Try multiple possible locations for .env file
      const possiblePaths = [
        path.join(workspaceFolder.uri.fsPath, 'uiorbit', '.env'), // Extension directory
        path.join(workspaceFolder.uri.fsPath, '.env'), // Workspace root
        path.join(__dirname, '..', '..', '.env'), // Extension source directory
        path.join(process.cwd(), '.env'), // Current working directory
      ];

      Logger.debug(`Workspace folder: ${workspaceFolder.uri.fsPath}`);
      Logger.debug(`__dirname: ${__dirname}`);
      Logger.debug(`process.cwd(): ${process.cwd()}`);

      for (const envPath of possiblePaths) {
        Logger.debug(`Checking for .env file at: ${envPath}`);
        if (await fs.pathExists(envPath)) {
          const envContent = await fs.readFile(envPath, 'utf-8');
          Logger.debug(`Raw .env content length: ${envContent.length}`);
          this.parseEnvContent(envContent);
          Logger.info(`Loaded configuration from .env file: ${envPath}`);
          Logger.debug(`Loaded ${Object.keys(this.envConfig).length} environment variables`);
          Logger.debug(`Environment variables: ${Object.keys(this.envConfig).join(', ')}`);
          return;
        }
      }

      Logger.debug('No .env file found in any expected location');
    } catch (error) {
      Logger.warn('Failed to load .env file:', error);
    }
  }

  /**
   * Parse .env file content
   */
  private parseEnvContent(content: string): void {
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Skip empty lines and comments
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        continue;
      }

      // Parse key=value pairs
      const equalIndex = trimmedLine.indexOf('=');
      if (equalIndex > 0) {
        const key = trimmedLine.substring(0, equalIndex).trim();
        const value = trimmedLine.substring(equalIndex + 1).trim();

        // Remove quotes if present
        const cleanValue = value.replace(/^["']|["']$/g, '');
        this.envConfig[key] = cleanValue;
      }
    }
  }

  /**
   * Load VS Code configuration
   */
  private async loadVSCodeConfig(): Promise<void> {
    const vsCodeConfig = vscode.workspace.getConfiguration('uiorbit');

    this.config = {
      openaiApiKey: this.getOpenAIApiKey(),
      defaultFramework: this.getDefaultFramework(),
      defaultStyling: this.getDefaultStyling(),
      enableAccessibility: this.isAccessibilityEnabled(),
      enableResponsiveDesign: this.isResponsiveDesignEnabled(),
      debugMode: this.isDebugMode(),
      apiRateLimit: this.getApiRateLimit(),
      maxConcurrentRequests: this.getMaxConcurrentRequests(),
    };

    Logger.debug('VS Code configuration loaded:', this.config);
  }

  /**
   * Dispose the service
   */
  dispose(): void {
    this.config = undefined;
    this.envConfig = {};
    Logger.debug('Configuration service disposed');
  }
}

/**
 * UIOrbit configuration interface
 */
export interface UIOrbitConfig {
  openaiApiKey: string;
  defaultFramework: string;
  defaultStyling: string;
  enableAccessibility: boolean;
  enableResponsiveDesign: boolean;
  debugMode: boolean;
  apiRateLimit: number;
  maxConcurrentRequests: number;
}
