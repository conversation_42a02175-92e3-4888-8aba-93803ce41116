import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';
import { ServiceRegistry } from '../core/ServiceRegistry';
import { TaskPlannerService, Task, TaskPlan, TaskProgress, TaskResult, TaskError } from './TaskPlannerService';
import { AIService } from './AIService';
import { FileOperationsService } from './FileOperationsService';
import { ASTAnalysisService } from './ASTAnalysisService';
import { ContextEngineService } from './ContextEngineService';

export interface ExecutionContext {
  planId: string;
  workspaceRoot: string;
  userPreferences: any;
  globalState: { [key: string]: any };
}

export interface TaskExecutor {
  canExecute(task: Task): boolean;
  execute(task: Task, context: ExecutionContext): Promise<TaskResult>;
  cancel?(taskId: string): Promise<void>;
}

export interface ExecutionOptions {
  parallel: boolean;
  maxConcurrentTasks: number;
  continueOnError: boolean;
  saveCheckpoints: boolean;
  userConfirmation: boolean;
}

export class TaskExecutorService {
  private taskPlanner: TaskPlannerService;
  private executors: Map<string, TaskExecutor> = new Map();
  private runningTasks: Map<string, Promise<TaskResult>> = new Map();
  private executionQueue: Task[] = [];
  private isExecuting = false;

  constructor(private serviceRegistry: ServiceRegistry) {
    this.taskPlanner = serviceRegistry.get<TaskPlannerService>('taskPlanner')!;
    this.initializeExecutors();
  }

  /**
   * Execute a task plan
   */
  async executePlan(planId: string, options: ExecutionOptions = this.getDefaultOptions()): Promise<void> {
    Logger.info(`Starting execution of plan: ${planId}`);

    const plan = this.taskPlanner.getPlan(planId);
    if (!plan) {
      throw new Error(`Plan not found: ${planId}`);
    }

    if (plan.status !== 'pending') {
      throw new Error(`Plan is not in pending status: ${plan.status}`);
    }

    try {
      const context: ExecutionContext = {
        planId,
        workspaceRoot: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '',
        userPreferences: {},
        globalState: {}
      };

      if (options.parallel) {
        await this.executeParallel(plan, context, options);
      } else {
        await this.executeSequential(plan, context, options);
      }

      Logger.info(`Plan execution completed: ${planId}`);

    } catch (error) {
      Logger.error(`Plan execution failed: ${planId}`, error);
      plan.status = 'failed';
      throw error;
    }
  }

  /**
   * Execute a single task
   */
  async executeTask(taskId: string, planId: string): Promise<TaskResult> {
    const plan = this.taskPlanner.getPlan(planId);
    if (!plan) {
      throw new Error(`Plan not found: ${planId}`);
    }

    const task = plan.tasks.find(t => t.id === taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    const context: ExecutionContext = {
      planId,
      workspaceRoot: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '',
      userPreferences: {},
      globalState: {}
    };

    return await this.executeSingleTask(task, context);
  }

  /**
   * Cancel task execution
   */
  async cancelExecution(planId: string): Promise<void> {
    const plan = this.taskPlanner.getPlan(planId);
    if (!plan) {
      return;
    }

    // Cancel all running tasks
    for (const [taskId, taskPromise] of this.runningTasks) {
      const task = plan.tasks.find(t => t.id === taskId);
      if (task) {
        task.status = 'cancelled';
        const executor = this.getExecutorForTask(task);
        if (executor?.cancel) {
          await executor.cancel(taskId);
        }
      }
    }

    this.runningTasks.clear();
    await this.taskPlanner.cancelPlan(planId);
    
    Logger.info(`Execution cancelled for plan: ${planId}`);
  }

  /**
   * Get execution status
   */
  getExecutionStatus(planId: string): any {
    const plan = this.taskPlanner.getPlan(planId);
    if (!plan) {
      return null;
    }

    return {
      planId,
      status: plan.status,
      progress: plan.progress,
      runningTasks: plan.tasks.filter(t => t.status === 'running').length,
      completedTasks: plan.tasks.filter(t => t.status === 'completed').length,
      failedTasks: plan.tasks.filter(t => t.status === 'failed').length
    };
  }

  private async executeSequential(plan: TaskPlan, context: ExecutionContext, options: ExecutionOptions): Promise<void> {
    plan.status = 'running';
    plan.startedAt = new Date();

    for (const task of plan.tasks) {
      if (plan.status !== 'running' && plan.status !== 'pending') {
        break;
      }

      // Check dependencies
      if (!this.areDependenciesMet(task, plan)) {
        Logger.warn(`Skipping task due to unmet dependencies: ${task.name}`);
        task.status = 'failed';
        if (!options.continueOnError) {
          throw new Error(`Dependencies not met for task: ${task.name}`);
        }
        continue;
      }

      try {
        const result = await this.executeSingleTask(task, context);
        
        if (!result.success && !options.continueOnError) {
          throw new Error(`Task failed: ${task.name} - ${result.message}`);
        }

        // Update global state with task outputs
        Object.assign(context.globalState, task.outputs);

      } catch (error) {
        Logger.error(`Task execution failed: ${task.name}`, error);
        task.status = 'failed';
        
        if (!options.continueOnError) {
          plan.status = 'failed';
          throw error;
        }
      }

      // Update plan progress
      this.taskPlanner.updatePlanProgress(plan.id);
    }

    plan.status = 'completed';
    plan.completedAt = new Date();
  }

  private async executeParallel(plan: TaskPlan, context: ExecutionContext, options: ExecutionOptions): Promise<void> {
    plan.status = 'running';
    plan.startedAt = new Date();

    const readyTasks = plan.tasks.filter(task => this.areDependenciesMet(task, plan));
    const promises: Promise<void>[] = [];

    while (readyTasks.length > 0 || promises.length > 0) {
      // Start new tasks up to the concurrency limit
      while (readyTasks.length > 0 && promises.length < options.maxConcurrentTasks) {
        const task = readyTasks.shift()!;
        const promise = this.executeSingleTask(task, context)
          .then(result => {
            if (result.success) {
              // Check for newly ready tasks
              const newlyReady = plan.tasks.filter(t => 
                t.status === 'pending' && 
                this.areDependenciesMet(t, plan) &&
                !readyTasks.includes(t)
              );
              readyTasks.push(...newlyReady);
            }
          })
          .catch(error => {
            Logger.error(`Parallel task execution failed: ${task.name}`, error);
            if (!options.continueOnError) {
              throw error;
            }
          });

        promises.push(promise);
      }

      // Wait for at least one task to complete
      if (promises.length > 0) {
        await Promise.race(promises);
        // Remove completed promises
        const stillRunning = promises.filter(p => {
          // This is a simplified check - in reality you'd need to track promise states
          return true; // Placeholder
        });
        promises.length = 0;
        promises.push(...stillRunning);
      }

      this.taskPlanner.updatePlanProgress(plan.id);
    }

    plan.status = 'completed';
    plan.completedAt = new Date();
  }

  private async executeSingleTask(task: Task, context: ExecutionContext): Promise<TaskResult> {
    Logger.info(`Executing task: ${task.name}`);

    task.status = 'running';
    task.metadata.startTime = new Date();

    // Report progress
    this.reportProgress(task, 0, 'Starting task...');

    try {
      const executor = this.getExecutorForTask(task);
      if (!executor) {
        throw new Error(`No executor found for task type: ${task.type}`);
      }

      // Add to running tasks
      const executionPromise = executor.execute(task, context);
      this.runningTasks.set(task.id, executionPromise);

      const result = await executionPromise;

      // Remove from running tasks
      this.runningTasks.delete(task.id);

      task.status = result.success ? 'completed' : 'failed';
      task.metadata.endTime = new Date();
      task.metadata.actualDuration = task.metadata.endTime.getTime() - task.metadata.startTime!.getTime();
      task.outputs = result.data || {};

      this.reportProgress(task, 100, result.message);

      Logger.info(`Task completed: ${task.name} (${result.success ? 'success' : 'failed'})`);
      return result;

    } catch (error) {
      this.runningTasks.delete(task.id);
      task.status = 'failed';
      task.metadata.endTime = new Date();
      task.metadata.actualDuration = task.metadata.endTime.getTime() - task.metadata.startTime!.getTime();

      const taskError: TaskError = {
        taskId: task.id,
        error: error instanceof Error ? error : new Error(String(error)),
        retryable: task.metadata.retryCount < task.metadata.maxRetries,
        context: context
      };

      if (task.onError) {
        task.onError(taskError);
      }

      Logger.error(`Task failed: ${task.name}`, error);
      throw error;
    }
  }

  private areDependenciesMet(task: Task, plan: TaskPlan): boolean {
    return task.dependencies.every(depName => {
      const depTask = plan.tasks.find(t => t.name === depName);
      return depTask && depTask.status === 'completed';
    });
  }

  private getExecutorForTask(task: Task): TaskExecutor | undefined {
    return this.executors.get(task.type);
  }

  private reportProgress(task: Task, progress: number, message: string): void {
    const progressData: TaskProgress = {
      taskId: task.id,
      progress,
      message,
      currentStep: message
    };

    if (task.onProgress) {
      task.onProgress(progressData);
    }

    // Emit to VS Code progress API if available
    // This would be implemented with actual VS Code progress reporting
  }

  private getDefaultOptions(): ExecutionOptions {
    return {
      parallel: false,
      maxConcurrentTasks: 3,
      continueOnError: false,
      saveCheckpoints: true,
      userConfirmation: false
    };
  }

  private initializeExecutors(): void {
    // Analysis executor
    this.executors.set('analysis', new AnalysisTaskExecutor(this.serviceRegistry));
    
    // Generation executor
    this.executors.set('generation', new GenerationTaskExecutor(this.serviceRegistry));
    
    // File operation executor
    this.executors.set('file-operation', new FileOperationTaskExecutor(this.serviceRegistry));
    
    // Validation executor
    this.executors.set('validation', new ValidationTaskExecutor(this.serviceRegistry));
    
    // Modification executor
    this.executors.set('modification', new ModificationTaskExecutor(this.serviceRegistry));
  }
}

// Task Executors Implementation
class AnalysisTaskExecutor implements TaskExecutor {
  constructor(private serviceRegistry: ServiceRegistry) {}

  canExecute(task: Task): boolean {
    return task.type === 'analysis';
  }

  async execute(task: Task, context: ExecutionContext): Promise<TaskResult> {
    const contextEngine = this.serviceRegistry.get<ContextEngineService>('contextEngineService')!;
    
    try {
      const result = await contextEngine.getContext({
        query: task.inputs.query || task.description,
        maxItems: 10
      });

      return {
        taskId: task.id,
        success: true,
        data: result,
        message: 'Analysis completed successfully',
        duration: 0
      };
    } catch (error) {
      return {
        taskId: task.id,
        success: false,
        data: null,
        message: `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: 0
      };
    }
  }
}

class GenerationTaskExecutor implements TaskExecutor {
  constructor(private serviceRegistry: ServiceRegistry) {}

  canExecute(task: Task): boolean {
    return task.type === 'generation';
  }

  async execute(task: Task, context: ExecutionContext): Promise<TaskResult> {
    const aiService = this.serviceRegistry.get<AIService>('ai')!;
    
    try {
      const result = await aiService.generateCode(
        task.inputs.prompt || task.description,
        task.inputs.options || {}
      );

      return {
        taskId: task.id,
        success: true,
        data: { generatedCode: result },
        message: 'Code generation completed successfully',
        duration: 0
      };
    } catch (error) {
      return {
        taskId: task.id,
        success: false,
        data: null,
        message: `Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: 0
      };
    }
  }
}

class FileOperationTaskExecutor implements TaskExecutor {
  constructor(private serviceRegistry: ServiceRegistry) {}

  canExecute(task: Task): boolean {
    return task.type === 'file-operation';
  }

  async execute(task: Task, context: ExecutionContext): Promise<TaskResult> {
    const fileOps = this.serviceRegistry.get<FileOperationsService>('fileOperations')!;
    
    try {
      if (task.inputs.operation === 'create') {
        const result = await fileOps.writeFile(task.inputs.path, task.inputs.content);
        return {
          taskId: task.id,
          success: result.success,
          data: result,
          message: result.success ? 'File created successfully' : result.error || 'File creation failed',
          duration: 0
        };
      }

      return {
        taskId: task.id,
        success: false,
        data: null,
        message: 'Unsupported file operation',
        duration: 0
      };
    } catch (error) {
      return {
        taskId: task.id,
        success: false,
        data: null,
        message: `File operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: 0
      };
    }
  }
}

class ValidationTaskExecutor implements TaskExecutor {
  constructor(private serviceRegistry: ServiceRegistry) {}

  canExecute(task: Task): boolean {
    return task.type === 'validation';
  }

  async execute(task: Task, context: ExecutionContext): Promise<TaskResult> {
    const astAnalysis = this.serviceRegistry.get<ASTAnalysisService>('astAnalysis')!;
    
    try {
      const result = await astAnalysis.analyzeFile(
        task.inputs.filePath || 'temp.ts',
        task.inputs.code || ''
      );

      const isValid = !result.errors || result.errors.length === 0;

      return {
        taskId: task.id,
        success: isValid,
        data: result,
        message: isValid ? 'Validation passed' : `Validation failed: ${result.errors?.join(', ')}`,
        duration: 0
      };
    } catch (error) {
      return {
        taskId: task.id,
        success: false,
        data: null,
        message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: 0
      };
    }
  }
}

class ModificationTaskExecutor implements TaskExecutor {
  constructor(private serviceRegistry: ServiceRegistry) {}

  canExecute(task: Task): boolean {
    return task.type === 'modification';
  }

  async execute(task: Task, context: ExecutionContext): Promise<TaskResult> {
    // Implementation for code modification tasks
    return {
      taskId: task.id,
      success: true,
      data: { modifiedCode: task.inputs.code },
      message: 'Modification completed',
      duration: 0
    };
  }
}
