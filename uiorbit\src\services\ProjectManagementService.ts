import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from '../utils/Logger';

export interface ProjectInfo {
  type: 'empty' | 'react' | 'vue' | 'angular' | 'vanilla' | 'unknown';
  framework?: string;
  hasPackageJson: boolean;
  hasNodeModules: boolean;
  hasComponents: boolean;
  rootPath: string;
  packageManager: 'npm' | 'yarn' | 'pnpm' | 'unknown';
}

export interface ProjectCreationOptions {
  framework: 'react' | 'vue' | 'angular';
  template: string;
  packageManager: 'npm' | 'yarn' | 'pnpm';
  styling: 'tailwind' | 'css' | 'styled-components';
  typescript: boolean;
}

/**
 * Service for managing project detection, creation, and configuration
 */
export class ProjectManagementService {
  private workspaceRoot: string;

  constructor() {
    this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
  }

  /**
   * Analyze the current workspace to determine project type and structure
   */
  async analyzeProject(): Promise<ProjectInfo> {
    try {
      Logger.info('Analyzing project structure...');

      if (!this.workspaceRoot) {
        throw new Error('No workspace folder found');
      }

      const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
      const nodeModulesPath = path.join(this.workspaceRoot, 'node_modules');
      
      const hasPackageJson = await fs.pathExists(packageJsonPath);
      const hasNodeModules = await fs.pathExists(nodeModulesPath);

      // Check if directory is empty
      const files = await fs.readdir(this.workspaceRoot);
      const nonHiddenFiles = files.filter(file => !file.startsWith('.'));
      
      if (nonHiddenFiles.length === 0) {
        Logger.info('Detected empty directory');
        return {
          type: 'empty',
          hasPackageJson: false,
          hasNodeModules: false,
          hasComponents: false,
          rootPath: this.workspaceRoot,
          packageManager: 'unknown'
        };
      }

      // Analyze package.json if it exists
      let projectType: ProjectInfo['type'] = 'unknown';
      let framework: string | undefined;
      let packageManager: ProjectInfo['packageManager'] = 'unknown';

      if (hasPackageJson) {
        const packageJson = await fs.readJson(packageJsonPath);
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

        // Detect framework
        if (dependencies.react || dependencies['@types/react']) {
          projectType = 'react';
          framework = 'React';
        } else if (dependencies.vue || dependencies['@vue/cli']) {
          projectType = 'vue';
          framework = 'Vue';
        } else if (dependencies['@angular/core']) {
          projectType = 'angular';
          framework = 'Angular';
        } else {
          projectType = 'vanilla';
        }

        // Detect package manager
        if (await fs.pathExists(path.join(this.workspaceRoot, 'yarn.lock'))) {
          packageManager = 'yarn';
        } else if (await fs.pathExists(path.join(this.workspaceRoot, 'pnpm-lock.yaml'))) {
          packageManager = 'pnpm';
        } else if (await fs.pathExists(path.join(this.workspaceRoot, 'package-lock.json'))) {
          packageManager = 'npm';
        }
      }

      // Check for components
      const hasComponents = await this.hasComponentFiles();

      const projectInfo: ProjectInfo = {
        type: projectType,
        framework,
        hasPackageJson,
        hasNodeModules,
        hasComponents,
        rootPath: this.workspaceRoot,
        packageManager
      };

      Logger.info(`Project analysis complete:`, projectInfo);
      return projectInfo;

    } catch (error) {
      Logger.error('Error analyzing project:', error);
      throw error;
    }
  }

  /**
   * Check if the project has component files
   */
  private async hasComponentFiles(): Promise<boolean> {
    try {
      const srcPath = path.join(this.workspaceRoot, 'src');
      const componentsPath = path.join(this.workspaceRoot, 'src', 'components');
      
      if (await fs.pathExists(componentsPath)) {
        const componentFiles = await fs.readdir(componentsPath);
        return componentFiles.some(file => 
          file.endsWith('.tsx') || 
          file.endsWith('.jsx') || 
          file.endsWith('.vue') ||
          file.endsWith('.ts') ||
          file.endsWith('.js')
        );
      }

      if (await fs.pathExists(srcPath)) {
        const srcFiles = await fs.readdir(srcPath);
        return srcFiles.some(file => 
          file.endsWith('.tsx') || 
          file.endsWith('.jsx') || 
          file.endsWith('.vue')
        );
      }

      return false;
    } catch (error) {
      Logger.error('Error checking for component files:', error);
      return false;
    }
  }

  /**
   * Create a new Vite React project in the current workspace
   */
  async createViteReactProject(options: Partial<ProjectCreationOptions> = {}): Promise<boolean> {
    try {
      Logger.info('Creating new Vite React project...');

      const defaultOptions: ProjectCreationOptions = {
        framework: 'react',
        template: 'react-ts',
        packageManager: 'npm',
        styling: 'tailwind',
        typescript: true,
        ...options
      };

      // Check if directory is empty or only has hidden files
      const files = await fs.readdir(this.workspaceRoot);
      const nonHiddenFiles = files.filter(file => !file.startsWith('.'));
      
      if (nonHiddenFiles.length > 0) {
        Logger.warn('Directory is not empty, cannot create new project');
        return false;
      }

      // Create Vite project using npm create
      const createCommand = this.buildCreateCommand(defaultOptions);
      Logger.info(`Running command: ${createCommand}`);

      // Execute the create command
      const success = await this.executeCommand(createCommand);
      
      if (success) {
        // Add additional configurations
        await this.configureProject(defaultOptions);
        Logger.info('Vite React project created successfully');
        return true;
      }

      return false;

    } catch (error) {
      Logger.error('Error creating Vite React project:', error);
      return false;
    }
  }

  /**
   * Build the create command based on options
   */
  private buildCreateCommand(options: ProjectCreationOptions): string {
    const { template, packageManager } = options;
    
    // Use create-vite with the current directory
    switch (packageManager) {
      case 'yarn':
        return `yarn create vite . --template ${template}`;
      case 'pnpm':
        return `pnpm create vite . --template ${template}`;
      default:
        return `npm create vite@latest . -- --template ${template}`;
    }
  }

  /**
   * Execute a command in the workspace directory
   */
  private async executeCommand(command: string): Promise<boolean> {
    return new Promise((resolve) => {
      const terminal = vscode.window.createTerminal({
        name: 'UIOrbit Project Setup',
        cwd: this.workspaceRoot
      });

      terminal.show();
      terminal.sendText(command);

      // Wait for command to complete (simplified - in production, we'd monitor the process)
      setTimeout(() => {
        resolve(true);
      }, 10000); // 10 second timeout
    });
  }

  /**
   * Configure the project after creation
   */
  private async configureProject(options: ProjectCreationOptions): Promise<void> {
    try {
      // Install additional dependencies based on styling choice
      if (options.styling === 'tailwind') {
        await this.installTailwind(options.packageManager);
      }

      // Create additional directories
      await this.createProjectStructure();

      // Update configuration files
      await this.updateProjectConfig(options);

    } catch (error) {
      Logger.error('Error configuring project:', error);
    }
  }

  /**
   * Install and configure Tailwind CSS
   */
  private async installTailwind(packageManager: string): Promise<void> {
    const installCommand = this.buildInstallCommand(
      packageManager,
      ['tailwindcss', 'postcss', 'autoprefixer'],
      true // dev dependencies
    );

    Logger.info('Installing Tailwind CSS...');
    await this.executeCommand(installCommand);
    
    // Initialize Tailwind
    await this.executeCommand('npx tailwindcss init -p');
  }

  /**
   * Build install command for dependencies
   */
  private buildInstallCommand(packageManager: string, packages: string[], dev: boolean = false): string {
    const devFlag = dev ? '-D' : '';
    const packagesStr = packages.join(' ');

    switch (packageManager) {
      case 'yarn':
        return `yarn add ${dev ? '--dev' : ''} ${packagesStr}`;
      case 'pnpm':
        return `pnpm add ${devFlag} ${packagesStr}`;
      default:
        return `npm install ${devFlag} ${packagesStr}`;
    }
  }

  /**
   * Create additional project structure
   */
  private async createProjectStructure(): Promise<void> {
    const directories = [
      'src/components',
      'src/components/ui',
      'src/hooks',
      'src/utils',
      'src/types',
      'src/styles'
    ];

    for (const dir of directories) {
      const dirPath = path.join(this.workspaceRoot, dir);
      await fs.ensureDir(dirPath);
    }

    Logger.info('Project structure created');
  }

  /**
   * Update project configuration files
   */
  private async updateProjectConfig(options: ProjectCreationOptions): Promise<void> {
    // Update tailwind.config.js if using Tailwind
    if (options.styling === 'tailwind') {
      await this.updateTailwindConfig();
    }

    // Update index.css with Tailwind directives
    await this.updateIndexCSS(options);
  }

  /**
   * Update Tailwind configuration
   */
  private async updateTailwindConfig(): Promise<void> {
    const configPath = path.join(this.workspaceRoot, 'tailwind.config.js');
    
    const config = `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`;

    await fs.writeFile(configPath, config);
    Logger.info('Tailwind config updated');
  }

  /**
   * Update index.css with styling setup
   */
  private async updateIndexCSS(options: ProjectCreationOptions): Promise<void> {
    const cssPath = path.join(this.workspaceRoot, 'src', 'index.css');
    
    let cssContent = '';
    
    if (options.styling === 'tailwind') {
      cssContent = `@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}`;
    }

    await fs.writeFile(cssPath, cssContent);
    Logger.info('CSS file updated');
  }

  /**
   * Check if the current workspace needs a project to be created
   */
  async shouldCreateProject(): Promise<boolean> {
    const projectInfo = await this.analyzeProject();
    return projectInfo.type === 'empty';
  }

  /**
   * Get the workspace root path
   */
  getWorkspaceRoot(): string {
    return this.workspaceRoot;
  }
}
