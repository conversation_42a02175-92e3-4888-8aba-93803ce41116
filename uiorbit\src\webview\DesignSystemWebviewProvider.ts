import * as vscode from 'vscode';
import { BaseWebviewProvider } from './BaseWebviewProvider';
import { DesignSystemAnalyzer } from '../services/DesignSystemAnalyzer';
import { Logger } from '../utils/Logger';

/**
 * Design System webview provider for UIOrbit extension
 * Provides design system analysis and visualization
 */
export class DesignSystemWebviewProvider extends BaseWebviewProvider {
  public static readonly viewType = 'uiorbit-design-system';

  private designSystemAnalyzer: DesignSystemAnalyzer;

  constructor(
    extensionUri: vscode.Uri,
    designSystemAnalyzer: DesignSystemAnalyzer
  ) {
    super(extensionUri, 'design-system.html');
    this.designSystemAnalyzer = designSystemAnalyzer;
  }

  protected async handleMessage(message: any): Promise<void> {
    try {
      switch (message.type) {
        case 'analyzeDesignSystem':
          await this.analyzeDesignSystem();
          break;

        case 'copyToClipboard':
          await this.copyToClipboard(message.text);
          break;

        case 'openFile':
          await this.openFile(message.path);
          break;

        case 'exportColors':
          await this.exportColors();
          break;

        case 'generateColorVariants':
          await this.generateColorVariants();
          break;

        case 'exportTypography':
          await this.exportTypography();
          break;

        case 'suggestImprovements':
          await this.suggestImprovements();
          break;

        case 'generateDocumentation':
          await this.generateDocumentation();
          break;

        case 'analyzeUsage':
          await this.analyzeUsage();
          break;

        case 'createSampleComponents':
          await this.createSampleComponents();
          break;

        default:
          Logger.warn(`Unknown message type: ${message.type}`);
      }
    } catch (error) {
      Logger.error('Error handling design system message:', error);
      this.showError('Failed to process design system request');
    }
  }

  protected onWebviewReady(): void {
    super.onWebviewReady();
    // Start analysis automatically
    this.analyzeDesignSystem();
  }

  private async analyzeDesignSystem(): Promise<void> {
    try {
      this.showLoading('Analyzing design system...');

      const analysis = await this.designSystemAnalyzer.analyzeWorkspaceDesignSystem();

      if (!analysis || analysis.components.length === 0) {
        this.postMessage({
          type: 'analysisComplete',
          data: null
        });
        return;
      }

      // Calculate consistency score
      const consistencyScore = this.calculateConsistencyScore(analysis);

      const analysisData = {
        components: analysis.components.map((comp: any) => ({
          name: comp.name,
          path: comp.filePath,
          props: comp.props?.join(', ') || 'No props',
          tags: comp.metadata?.tags || []
        })),
        colors: this.flattenColors(analysis.tokens.colors),
        typography: this.flattenTypography(analysis.tokens.typography),
        consistencyScore: consistencyScore
      };

      this.postMessage({
        type: 'analysisComplete',
        data: analysisData
      });

      this.hideLoading();

    } catch (error) {
      Logger.error('Error analyzing design system:', error);
      this.postMessage({
        type: 'analysisError',
        error: 'Failed to analyze design system. Please ensure your project contains React/Vue components.'
      });
      this.hideLoading();
    }
  }

  private calculateConsistencyScore(analysis: any): number {
    // Simple consistency scoring algorithm
    let score = 100;

    // Penalize for too many different colors
    if (analysis.colors && analysis.colors.length > 20) {
      score -= Math.min(30, (analysis.colors.length - 20) * 2);
    }

    // Penalize for too many different font families
    if (analysis.typography && analysis.typography.length > 5) {
      score -= Math.min(20, (analysis.typography.length - 5) * 4);
    }

    // Bonus for having a reasonable number of components
    if (analysis.components && analysis.components.length >= 5 && analysis.components.length <= 50) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  private async copyToClipboard(text: string): Promise<void> {
    try {
      await vscode.env.clipboard.writeText(text);
      vscode.window.showInformationMessage(`Copied "${text}" to clipboard`);
    } catch (error) {
      Logger.error('Error copying to clipboard:', error);
      vscode.window.showErrorMessage('Failed to copy to clipboard');
    }
  }

  private async openFile(filePath: string): Promise<void> {
    try {
      const uri = vscode.Uri.file(filePath);
      await vscode.window.showTextDocument(uri);
    } catch (error) {
      Logger.error('Error opening file:', error);
      vscode.window.showErrorMessage(`Failed to open file: ${filePath}`);
    }
  }

  private async exportColors(): Promise<void> {
    try {
      const analysis = await this.designSystemAnalyzer.analyzeWorkspaceDesignSystem();
      
      if (!analysis || !analysis.tokens.colors) {
        vscode.window.showWarningMessage('No colors found to export');
        return;
      }

      // Generate CSS custom properties
      let cssContent = ':root {\n';
      const colors = this.flattenColors(analysis.tokens.colors);
      colors.forEach((color: any) => {
        const varName = color.name.toLowerCase().replace(/\s+/g, '-');
        cssContent += `  --color-${varName}: ${color.value};\n`;
      });
      cssContent += '}\n';

      // Save to file
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (workspaceFolder) {
        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, 'design-system-colors.css');
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(cssContent));
        vscode.window.showInformationMessage('Colors exported to design-system-colors.css');
      }

    } catch (error) {
      Logger.error('Error exporting colors:', error);
      vscode.window.showErrorMessage('Failed to export colors');
    }
  }

  private async generateColorVariants(): Promise<void> {
    try {
      vscode.window.showInformationMessage('Color variant generation coming soon!');
    } catch (error) {
      Logger.error('Error generating color variants:', error);
    }
  }

  private async exportTypography(): Promise<void> {
    try {
      const analysis = await this.designSystemAnalyzer.analyzeWorkspaceDesignSystem();
      
      if (!analysis || !analysis.tokens.typography) {
        vscode.window.showWarningMessage('No typography found to export');
        return;
      }

      // Generate CSS classes
      let cssContent = '/* Typography System */\n\n';
      const typography = this.flattenTypography(analysis.tokens.typography);
      typography.forEach((font: any, index: number) => {
        const className = font.name ? font.name.toLowerCase().replace(/\s+/g, '-') : `font-${index + 1}`;
        cssContent += `.${className} {\n`;
        cssContent += `  font-family: ${font.family};\n`;
        cssContent += `  font-size: ${font.size};\n`;
        cssContent += `  font-weight: ${font.weight};\n`;
        cssContent += '}\n\n';
      });

      // Save to file
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
      if (workspaceFolder) {
        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, 'design-system-typography.css');
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(cssContent));
        vscode.window.showInformationMessage('Typography exported to design-system-typography.css');
      }

    } catch (error) {
      Logger.error('Error exporting typography:', error);
      vscode.window.showErrorMessage('Failed to export typography');
    }
  }

  private async suggestImprovements(): Promise<void> {
    try {
      vscode.window.showInformationMessage('Design improvement suggestions coming soon!');
    } catch (error) {
      Logger.error('Error suggesting improvements:', error);
    }
  }

  private async generateDocumentation(): Promise<void> {
    try {
      vscode.window.showInformationMessage('Documentation generation coming soon!');
    } catch (error) {
      Logger.error('Error generating documentation:', error);
    }
  }

  private async analyzeUsage(): Promise<void> {
    try {
      vscode.window.showInformationMessage('Usage analysis coming soon!');
    } catch (error) {
      Logger.error('Error analyzing usage:', error);
    }
  }

  private async createSampleComponents(): Promise<void> {
    try {
      vscode.window.showInformationMessage('Sample component creation coming soon!');
    } catch (error) {
      Logger.error('Error creating sample components:', error);
    }
  }

  /**
   * Flatten colors from the design system structure
   */
  private flattenColors(colors: any): any[] {
    const flattened: any[] = [];

    if (colors.primary) {
      colors.primary.forEach((color: any) => flattened.push({ ...color, category: 'primary' }));
    }
    if (colors.secondary) {
      colors.secondary.forEach((color: any) => flattened.push({ ...color, category: 'secondary' }));
    }
    if (colors.neutral) {
      colors.neutral.forEach((color: any) => flattened.push({ ...color, category: 'neutral' }));
    }
    if (colors.accent) {
      colors.accent.forEach((color: any) => flattened.push({ ...color, category: 'accent' }));
    }

    return flattened;
  }

  /**
   * Flatten typography from the design system structure
   */
  private flattenTypography(typography: any): any[] {
    const flattened: any[] = [];

    if (typography.fontFamilies) {
      Object.entries(typography.fontFamilies).forEach(([key, family]) => {
        flattened.push({
          name: key,
          family: family,
          size: '1rem',
          weight: '400'
        });
      });
    }

    return flattened;
  }

  /**
   * Public method to refresh the analysis
   */
  public refresh(): void {
    super.refresh();
    this.analyzeDesignSystem();
  }
}
