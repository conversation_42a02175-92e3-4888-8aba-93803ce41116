import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';

export interface TestSuite {
  name: string;
  description: string;
  tests: TestCase[];
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
  timeout?: number;
}

export interface TestCase {
  name: string;
  description: string;
  test: () => Promise<void>;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
}

export interface TestResult {
  suiteName: string;
  testName: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: Error;
  logs: string[];
}

export interface TestSuiteResult {
  suiteName: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  results: TestResult[];
}

export interface TestRunOptions {
  pattern?: string;
  timeout?: number;
  bail?: boolean;
  verbose?: boolean;
  coverage?: boolean;
}

export class TestRunner {
  private testSuites: Map<string, TestSuite> = new Map();
  private isRunning = false;

  /**
   * Register a test suite
   */
  registerSuite(suite: TestSuite): void {
    this.testSuites.set(suite.name, suite);
    Logger.info(`Test suite registered: ${suite.name}`);
  }

  /**
   * Run all test suites
   */
  async runAll(options: TestRunOptions = {}): Promise<TestSuiteResult[]> {
    if (this.isRunning) {
      throw new Error('Tests are already running');
    }

    this.isRunning = true;
    const results: TestSuiteResult[] = [];

    try {
      Logger.info('Starting test run...');

      for (const [suiteName, suite] of this.testSuites) {
        if (options.pattern && !suiteName.includes(options.pattern)) {
          continue;
        }

        const result = await this.runSuite(suite, options);
        results.push(result);

        if (options.bail && result.status === 'failed') {
          Logger.info('Stopping test run due to failure (bail option)');
          break;
        }
      }

      this.logSummary(results);
      return results;

    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Run a specific test suite
   */
  async runSuite(suite: TestSuite, options: TestRunOptions = {}): Promise<TestSuiteResult> {
    Logger.info(`Running test suite: ${suite.name}`);

    const startTime = Date.now();
    const results: TestResult[] = [];
    let suiteStatus: 'passed' | 'failed' | 'skipped' = 'passed';

    try {
      // Run setup
      if (suite.setup) {
        await this.runWithTimeout(suite.setup, suite.timeout || 30000);
      }

      // Run tests
      for (const testCase of suite.tests) {
        if (testCase.skip) {
          results.push({
            suiteName: suite.name,
            testName: testCase.name,
            status: 'skipped',
            duration: 0,
            logs: []
          });
          continue;
        }

        const testResult = await this.runTest(suite.name, testCase, options);
        results.push(testResult);

        if (testResult.status === 'failed') {
          suiteStatus = 'failed';
          if (options.bail) {
            break;
          }
        }
      }

    } catch (error) {
      Logger.error(`Test suite setup/teardown failed: ${suite.name}`, error);
      suiteStatus = 'failed';
    } finally {
      // Run teardown
      try {
        if (suite.teardown) {
          await this.runWithTimeout(suite.teardown, suite.timeout || 30000);
        }
      } catch (error) {
        Logger.error(`Test suite teardown failed: ${suite.name}`, error);
      }
    }

    const duration = Date.now() - startTime;
    const passedTests = results.filter(r => r.status === 'passed').length;
    const failedTests = results.filter(r => r.status === 'failed').length;
    const skippedTests = results.filter(r => r.status === 'skipped').length;

    const suiteResult: TestSuiteResult = {
      suiteName: suite.name,
      status: suiteStatus,
      duration,
      totalTests: results.length,
      passedTests,
      failedTests,
      skippedTests,
      results
    };

    Logger.info(`Test suite completed: ${suite.name} (${passedTests}/${results.length} passed)`);
    return suiteResult;
  }

  /**
   * Run a specific test
   */
  async runTest(suiteName: string, testCase: TestCase, options: TestRunOptions = {}): Promise<TestResult> {
    const startTime = Date.now();
    const logs: string[] = [];

    // Capture logs during test execution
    const originalLog = Logger.info;
    Logger.info = (message: string, ...args: any[]) => {
      logs.push(`[INFO] ${message}`);
      if (options.verbose) {
        originalLog(message, ...args);
      }
    };

    try {
      await this.runWithTimeout(testCase.test, testCase.timeout || 10000);

      const duration = Date.now() - startTime;
      Logger.info = originalLog; // Restore original logger

      if (options.verbose) {
        Logger.info(`✓ ${testCase.name} (${duration}ms)`);
      }

      return {
        suiteName,
        testName: testCase.name,
        status: 'passed',
        duration,
        logs
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      Logger.info = originalLog; // Restore original logger

      Logger.error(`✗ ${testCase.name} (${duration}ms)`, error);

      return {
        suiteName,
        testName: testCase.name,
        status: 'failed',
        duration,
        error: error instanceof Error ? error : new Error(String(error)),
        logs
      };
    }
  }

  /**
   * Get test coverage report
   */
  async getCoverageReport(): Promise<any> {
    // This would integrate with a coverage tool like nyc or c8
    return {
      lines: { total: 0, covered: 0, percentage: 0 },
      functions: { total: 0, covered: 0, percentage: 0 },
      branches: { total: 0, covered: 0, percentage: 0 },
      statements: { total: 0, covered: 0, percentage: 0 }
    };
  }

  /**
   * Clear all registered test suites
   */
  clear(): void {
    this.testSuites.clear();
    Logger.info('All test suites cleared');
  }

  /**
   * Get registered test suites
   */
  getSuites(): TestSuite[] {
    return Array.from(this.testSuites.values());
  }

  private async runWithTimeout<T>(fn: () => Promise<T>, timeout: number): Promise<T> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Test timed out after ${timeout}ms`));
      }, timeout);

      fn()
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  private logSummary(results: TestSuiteResult[]): void {
    const totalSuites = results.length;
    const passedSuites = results.filter(r => r.status === 'passed').length;
    const failedSuites = results.filter(r => r.status === 'failed').length;

    const totalTests = results.reduce((sum, r) => sum + r.totalTests, 0);
    const passedTests = results.reduce((sum, r) => sum + r.passedTests, 0);
    const failedTests = results.reduce((sum, r) => sum + r.failedTests, 0);
    const skippedTests = results.reduce((sum, r) => sum + r.skippedTests, 0);

    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

    Logger.info('\n=== Test Summary ===');
    Logger.info(`Suites: ${passedSuites}/${totalSuites} passed`);
    Logger.info(`Tests: ${passedTests}/${totalTests} passed, ${failedTests} failed, ${skippedTests} skipped`);
    Logger.info(`Duration: ${totalDuration}ms`);

    if (failedTests > 0) {
      Logger.info('\n=== Failed Tests ===');
      results.forEach(suite => {
        suite.results
          .filter(test => test.status === 'failed')
          .forEach(test => {
            Logger.error(`${suite.suiteName} > ${test.testName}: ${test.error?.message}`);
          });
      });
    }
  }
}

// Test utilities and assertions
export class TestAssertions {
  static assertEqual<T>(actual: T, expected: T, message?: string): void {
    if (actual !== expected) {
      throw new Error(message || `Expected ${expected}, but got ${actual}`);
    }
  }

  static assertNotEqual<T>(actual: T, expected: T, message?: string): void {
    if (actual === expected) {
      throw new Error(message || `Expected values to be different, but both were ${actual}`);
    }
  }

  static assertTrue(condition: boolean, message?: string): void {
    if (!condition) {
      throw new Error(message || 'Expected condition to be true');
    }
  }

  static assertFalse(condition: boolean, message?: string): void {
    if (condition) {
      throw new Error(message || 'Expected condition to be false');
    }
  }

  static assertThrows(fn: () => void, message?: string): void {
    try {
      fn();
      throw new Error(message || 'Expected function to throw an error');
    } catch (error) {
      // Expected behavior
    }
  }

  static async assertThrowsAsync(fn: () => Promise<void>, message?: string): Promise<void> {
    try {
      await fn();
      throw new Error(message || 'Expected async function to throw an error');
    } catch (error) {
      // Expected behavior
    }
  }

  static assertContains<T>(array: T[], item: T, message?: string): void {
    if (!array.includes(item)) {
      throw new Error(message || `Expected array to contain ${item}`);
    }
  }

  static assertNotContains<T>(array: T[], item: T, message?: string): void {
    if (array.includes(item)) {
      throw new Error(message || `Expected array to not contain ${item}`);
    }
  }

  static assertInstanceOf<T>(obj: any, constructor: new (...args: any[]) => T, message?: string): void {
    if (!(obj instanceof constructor)) {
      throw new Error(message || `Expected object to be instance of ${constructor.name}`);
    }
  }

  static assertUndefined(value: any, message?: string): void {
    if (value !== undefined) {
      throw new Error(message || `Expected value to be undefined, but got ${value}`);
    }
  }

  static assertNotUndefined(value: any, message?: string): void {
    if (value === undefined) {
      throw new Error(message || 'Expected value to not be undefined');
    }
  }

  static assertNull(value: any, message?: string): void {
    if (value !== null) {
      throw new Error(message || `Expected value to be null, but got ${value}`);
    }
  }

  static assertNotNull(value: any, message?: string): void {
    if (value === null) {
      throw new Error(message || 'Expected value to not be null');
    }
  }
}

// Mock utilities for testing
export class MockService {
  private mocks: Map<string, any> = new Map();

  mock<T>(serviceName: string, implementation: Partial<T>): void {
    this.mocks.set(serviceName, implementation);
  }

  get<T>(serviceName: string): T | undefined {
    return this.mocks.get(serviceName);
  }

  clear(): void {
    this.mocks.clear();
  }

  restore(): void {
    this.clear();
  }
}

// Test data generators
export class TestDataGenerator {
  static randomString(length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  static randomNumber(min: number = 0, max: number = 100): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  static randomBoolean(): boolean {
    return Math.random() < 0.5;
  }

  static randomArray<T>(generator: () => T, length: number = 5): T[] {
    return Array.from({ length }, generator);
  }

  static mockComponentSpec(): any {
    return {
      name: this.randomString(8),
      type: 'functional',
      framework: 'react',
      styling: 'tailwind',
      props: [
        {
          name: 'className',
          type: 'string',
          required: false,
          description: 'Additional CSS classes'
        }
      ],
      accessibility: true,
      responsive: true
    };
  }

  static mockTaskPlan(): any {
    return {
      id: this.randomString(12),
      name: `Test Plan ${this.randomString(5)}`,
      description: 'A test task plan',
      tasks: [
        {
          id: this.randomString(12),
          name: 'Test Task',
          description: 'A test task',
          type: 'analysis',
          status: 'pending',
          priority: 1,
          dependencies: [],
          inputs: {},
          outputs: {},
          metadata: {
            estimatedDuration: 5000,
            retryCount: 0,
            maxRetries: 3
          }
        }
      ],
      totalEstimatedDuration: 5000,
      status: 'pending',
      createdAt: new Date(),
      progress: {
        completedTasks: 0,
        totalTasks: 1,
        overallProgress: 0
      }
    };
  }
}
