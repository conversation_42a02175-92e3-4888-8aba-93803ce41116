import OpenAI from 'openai';

import * as vscode from 'vscode';

import { Logger } from '../utils/Logger';

import { ConfigurationService } from './ConfigurationService';
import { NaturalLanguageProcessor, UserIntent, CodeContext } from './NaturalLanguageProcessor';
import { ProjectDetectionService, ProjectContext as DetectedProjectContext } from './ProjectDetectionService';
import { UsageTrackingService } from './UsageTrackingService';

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  isError: boolean;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface LegacyProjectContext {
  framework?: string;
  hasPackageJson?: boolean;
  hasReactComponents?: boolean;
  hasVueComponents?: boolean;
  hasAngularComponents?: boolean;
  hasTailwind?: boolean;
  hasStyledComponents?: boolean;
  projectType?: 'react' | 'vue' | 'angular' | 'svelte' | 'vanilla' | 'unknown' | 'none';
}

export class AIService {
  private openai: OpenAI | null = null;
  private conversationHistory: ChatMessage[] = [];
  private projectContext: LegacyProjectContext = {};
  private readonly maxHistoryLength = 20; // Keep last 20 messages
  private nlp: NaturalLanguageProcessor;
  private projectDetectionService?: ProjectDetectionService;
  private usageTrackingService?: UsageTrackingService;

  constructor(private configService: ConfigurationService) {
    this.nlp = new NaturalLanguageProcessor();
    // Don't initialize OpenAI in constructor - wait for first use
  }

  /**
   * Set project detection service (injected after construction)
   */
  setProjectDetectionService(service: ProjectDetectionService): void {
    this.projectDetectionService = service;
  }

  /**
   * Set usage tracking service (injected after construction)
   */
  setUsageTrackingService(service: UsageTrackingService): void {
    this.usageTrackingService = service;
  }

  /**
   * Initialize the AI service
   */
  async initialize(): Promise<void> {
    await this.initializeOpenAI();
  }

  /**
   * Initialize OpenAI client
   */
  private async initializeOpenAI(): Promise<void> {
    try {
      const apiKey = this.configService.getOpenAIApiKey();

      Logger.debug(`API key length: ${apiKey ? apiKey.length : 0}`);
      Logger.debug(`API key starts with: ${apiKey ? apiKey.substring(0, 10) + '...' : 'undefined'}`);

      if (!apiKey) {
        Logger.warn('OpenAI API key not configured');
        return;
      }

      this.openai = new OpenAI({
        apiKey: apiKey,
      });

      Logger.info('OpenAI client initialized successfully');
    } catch (error) {
      Logger.error('Failed to initialize OpenAI client:', error);
    }
  }

  /**
   * Process chat message and generate AI response
   */
  async processMessage(userMessage: string): Promise<AIResponse> {
    try {
      // Check usage limits first
      if (this.usageTrackingService) {
        const usageCheck = await this.usageTrackingService.canMakeRequest();
        if (!usageCheck.allowed) {
          await this.usageTrackingService.showUpgradePrompt();
          return {
            content: `🚀 **Community Limit Reached**\n\nYou've used all ${usageCheck.limit} AI requests for this month. Upgrade to Professional for unlimited requests!\n\n[Upgrade to Professional](https://uiorbit.com/upgrade) | [View Pricing](https://uiorbit.com/pricing)`,
            isError: true
          };
        }

        // Show remaining requests if getting low
        if (usageCheck.remaining <= 10) {
          const stats = await this.usageTrackingService.getUsageStats();
          Logger.info(`Usage warning: ${stats.remaining} requests remaining`);
        }
      }

      if (!this.openai) {
        await this.initializeOpenAI();

        if (!this.openai) {
          return {
            content: "Please configure your OpenAI API key in VS Code settings (UIOrbit > OpenAI API Key) to enable AI functionality.",
            isError: true
          };
        }
      }

      // Parse user intent using NLP
      const codeContext: CodeContext = {
        projectType: this.projectContext.projectType,
        framework: this.projectContext.framework,
        currentFile: this.getCurrentFileName(),
        selectedText: await this.getSelectedText()
      };

      const userIntent = await this.nlp.parseIntent(userMessage, codeContext);
      Logger.info('Parsed user intent:', userIntent);

      // Add user message to history
      this.addToHistory('user', userMessage);

      // Generate enhanced system prompt based on intent and context
      const systemPrompt = this.generateEnhancedSystemPrompt(userIntent, codeContext);

      // Prepare messages for OpenAI
      const messages: ChatMessage[] = [
        { role: 'system', content: systemPrompt },
        ...this.conversationHistory
      ];

      Logger.info(`Sending request to OpenAI with ${messages.length} messages`);

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: messages,
        max_tokens: 2000,
        temperature: 0.7,
        stream: false,
      });

      const assistantMessage = response.choices[0]?.message?.content;

      if (!assistantMessage) {
        throw new Error('No response from OpenAI');
      }

      // Add assistant response to history
      this.addToHistory('assistant', assistantMessage);

      // Record successful request for usage tracking
      if (this.usageTrackingService) {
        await this.usageTrackingService.recordRequest();
      }

      return {
        content: assistantMessage,
        isError: false,
        usage: {
          promptTokens: response.usage?.prompt_tokens || 0,
          completionTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0,
        }
      };

    } catch (error) {
      Logger.error('Error processing AI message:', error);

      let errorMessage = 'Sorry, I encountered an error processing your request.';

      if (error instanceof Error) {
        Logger.error('Detailed error message:', error.message);
        Logger.error('Error stack:', error.stack);

        if (error.message.includes('API key')) {
          errorMessage = `Invalid OpenAI API key. Please check your configuration. Error: ${error.message}`;
        } else if (error.message.includes('quota')) {
          errorMessage = 'OpenAI API quota exceeded. Please check your billing.';
        } else if (error.message.includes('rate limit')) {
          errorMessage = 'Rate limit exceeded. Please wait a moment and try again.';
        } else {
          errorMessage = `Error: ${error.message}`;
        }
      }

      return {
        content: errorMessage,
        isError: true
      };
    }
  }

  /**
   * Generate enhanced system prompt based on intent and context
   */
  private generateEnhancedSystemPrompt(intent: UserIntent, context: CodeContext): string {
    const basePrompt = this.generateSystemPrompt();

    let enhancedPrompt = basePrompt;

    // Add intent-specific instructions
    enhancedPrompt += `\n\n**Current User Intent:**\n`;
    enhancedPrompt += `- Action: ${intent.action}\n`;
    enhancedPrompt += `- Target: ${intent.target}\n`;
    enhancedPrompt += `- Complexity: ${intent.complexity || 'medium'}\n`;

    if (intent.framework) {
      enhancedPrompt += `- Preferred Framework: ${intent.framework}\n`;
    }

    if (intent.styling) {
      enhancedPrompt += `- Preferred Styling: ${intent.styling}\n`;
    }

    if (intent.requirements.length > 0) {
      enhancedPrompt += `- Requirements: ${intent.requirements.join(', ')}\n`;
    }

    // Add context information
    if (context.currentFile) {
      enhancedPrompt += `\n**Current Context:**\n`;
      enhancedPrompt += `- Current File: ${context.currentFile}\n`;
    }

    if (context.selectedText) {
      enhancedPrompt += `- Selected Code: ${context.selectedText.substring(0, 200)}...\n`;
    }

    // Add action-specific guidance
    switch (intent.action) {
      case 'generate':
        enhancedPrompt += `\n**Generation Guidelines:**\n`;
        enhancedPrompt += `- Create clean, modern, accessible code\n`;
        enhancedPrompt += `- Follow current best practices\n`;
        enhancedPrompt += `- Include proper TypeScript types if applicable\n`;
        enhancedPrompt += `- Add helpful comments\n`;
        enhancedPrompt += `- Include GSAP animations where appropriate\n`;
        enhancedPrompt += `- Implement responsive design with modern CSS\n`;
        enhancedPrompt += `- Add smooth scroll effects and micro-interactions\n`;
        enhancedPrompt += `- Ensure full accessibility compliance\n`;

        if (intent.target === 'project') {
          enhancedPrompt += `\n**Complete Project Generation:**\n`;
          enhancedPrompt += `- Generate entire project structure\n`;
          enhancedPrompt += `- Include all necessary files and dependencies\n`;
          enhancedPrompt += `- Add professional animations and effects\n`;
          enhancedPrompt += `- Implement modern UI/UX patterns\n`;
          enhancedPrompt += `- Create production-ready code\n`;
        }
        break;
      case 'modify':
        enhancedPrompt += `\n**Modification Guidelines:**\n`;
        enhancedPrompt += `- Preserve existing functionality\n`;
        enhancedPrompt += `- Improve code quality and readability\n`;
        enhancedPrompt += `- Suggest modern alternatives when appropriate\n`;
        break;
      case 'debug':
        enhancedPrompt += `\n**Debugging Guidelines:**\n`;
        enhancedPrompt += `- Identify potential issues systematically\n`;
        enhancedPrompt += `- Provide clear explanations\n`;
        enhancedPrompt += `- Suggest multiple solutions when possible\n`;
        break;
    }

    return enhancedPrompt;
  }

  /**
   * Generate system prompt based on project context
   */
  private generateSystemPrompt(): string {
    const basePrompt = `You are UIOrbit, a world-class AI frontend development assistant that works like a top-tier senior developer. You have advanced capabilities including:

🚀 **Complete Project Generation**: Create entire projects from descriptions (AI headshot generators, e-commerce sites, dashboards, etc.)
🎯 **Intelligent Code Modification**: Find and modify specific code (e.g., "change navbar color to blue")
🎨 **Advanced Component Creation**: Generate components with GSAP animations, scroll effects, and modern patterns
🔧 **Smart Codebase Understanding**: Analyze project structure, dependencies, and patterns

**Technical Expertise:**
🎨 **Frontend Frameworks**: React, Vue, Angular, Svelte, Next.js, Nuxt.js, Remix
🎯 **Styling Solutions**: Tailwind CSS, styled-components, Emotion, SCSS, CSS Modules
🚀 **Modern Tools**: Vite, Webpack, TypeScript, ESLint, Prettier, Storybook
✨ **UI Libraries**: Material-UI, Ant Design, Chakra UI, Headless UI, Mantine
🎭 **Animations**: GSAP (ScrollTrigger, Timeline), Framer Motion, Lottie, CSS animations
📱 **Responsive Design**: Mobile-first, accessibility (WCAG), performance optimization
🎪 **Advanced Features**: Scroll effects, parallax, micro-interactions, smooth transitions

**Your Capabilities:**
1. **Complete Project Generation**: Generate entire production-ready projects with proper structure, animations, and modern patterns
2. **Intelligent Code Modification**: Understand existing codebases and make precise modifications
3. **Advanced Component Creation**: Create components with professional animations and accessibility
4. **Modern UI/UX Implementation**: Apply current design trends and best practices
5. **Performance Optimization**: Implement code splitting, lazy loading, and optimization techniques

**Communication Style:**
- Provide complete, production-ready solutions
- Include modern animations and effects by default
- Focus on accessibility and performance
- Use current best practices and patterns
- Be thorough but practical`;

    // Add project-specific context
    let contextPrompt = '';
    
    if (this.projectContext.projectType) {
      contextPrompt += `\n\n**Current Project Context:**\n`;
      contextPrompt += `- Framework: ${this.projectContext.projectType}\n`;
      
      if (this.projectContext.hasTailwind) {
        contextPrompt += `- Styling: Tailwind CSS\n`;
      }
      
      if (this.projectContext.hasStyledComponents) {
        contextPrompt += `- Styling: styled-components\n`;
      }
    }

    return basePrompt + contextPrompt;
  }

  /**
   * Add message to conversation history
   */
  private addToHistory(role: 'user' | 'assistant', content: string): void {
    this.conversationHistory.push({ role, content });

    // Keep history within limits
    if (this.conversationHistory.length > this.maxHistoryLength) {
      this.conversationHistory = this.conversationHistory.slice(-this.maxHistoryLength);
    }
  }

  /**
   * Update project context based on workspace analysis
   */
  async updateProjectContext(): Promise<void> {
    try {
      if (this.projectDetectionService) {
        // Use the enhanced project detection service
        const context = await this.projectDetectionService.getProjectContext();
        this.projectContext = {
          hasPackageJson: context.hasProject,
          projectType: context.projectType === 'none' ? 'unknown' : context.projectType,
          hasReactComponents: context.projectType === 'react',
          hasVueComponents: context.projectType === 'vue',
          hasAngularComponents: context.projectType === 'angular',
          hasTailwind: context.styling === 'tailwind',
          hasStyledComponents: context.styling === 'styled-components',
          framework: context.framework
        };

        Logger.info('Project context updated with enhanced detection:', this.projectContext);
      } else {
        // Fallback to basic detection
        await this.basicProjectDetection();
      }

    } catch (error) {
      Logger.error('Error updating project context:', error);
      await this.basicProjectDetection();
    }
  }

  /**
   * Basic project detection fallback
   */
  private async basicProjectDetection(): Promise<void> {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders || workspaceFolders.length === 0) {
        return;
      }

      // Check for package.json
      try {
        const packageJsonUri = vscode.Uri.joinPath(workspaceFolders[0].uri, 'package.json');
        const packageJsonContent = await vscode.workspace.fs.readFile(packageJsonUri);
        const packageJson = JSON.parse(packageJsonContent.toString());

        this.projectContext.hasPackageJson = true;

        // Detect framework
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

        if (dependencies.react) {
          this.projectContext.projectType = 'react';
          this.projectContext.hasReactComponents = true;
        } else if (dependencies.vue) {
          this.projectContext.projectType = 'vue';
          this.projectContext.hasVueComponents = true;
        } else if (dependencies['@angular/core']) {
          this.projectContext.projectType = 'angular';
          this.projectContext.hasAngularComponents = true;
        } else {
          this.projectContext.projectType = 'vanilla';
        }

        // Detect styling solutions
        this.projectContext.hasTailwind = !!(dependencies.tailwindcss || dependencies['@tailwindcss/forms']);
        this.projectContext.hasStyledComponents = !!(dependencies['styled-components'] || dependencies['@emotion/styled']);

        Logger.info('Basic project context updated:', this.projectContext);

      } catch (error) {
        // No package.json found, assume vanilla project
        this.projectContext.projectType = 'vanilla';
        Logger.info('No package.json found, assuming vanilla project');
      }

    } catch (error) {
      Logger.error('Error in basic project detection:', error);
    }
  }

  /**
   * Clear conversation history
   */
  clearHistory(): void {
    this.conversationHistory = [];
    Logger.info('Conversation history cleared');
  }

  /**
   * Get current conversation history
   */
  getHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }

  /**
   * Get project context
   */
  getProjectContext(): LegacyProjectContext {
    return { ...this.projectContext };
  }

  /**
   * Check if AI service is ready
   */
  isReady(): boolean {
    return this.openai !== null;
  }

  /**
   * Get current file name from active editor
   */
  private getCurrentFileName(): string | undefined {
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor) {
      return activeEditor.document.fileName;
    }
    return undefined;
  }

  /**
   * Get selected text from active editor
   */
  private async getSelectedText(): Promise<string | undefined> {
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor && !activeEditor.selection.isEmpty) {
      return activeEditor.document.getText(activeEditor.selection);
    }
    return undefined;
  }

  /**
   * Generate code using AI with specific options
   */
  async generateCode(prompt: string, options: {
    language?: string;
    maxTokens?: number;
    temperature?: number;
  } = {}): Promise<string> {
    if (!this.openai) {
      throw new Error('OpenAI not initialized. Please check your API key.');
    }

    try {
      const systemPrompt = `You are an expert ${options.language || 'TypeScript'} developer. Generate clean, production-ready code based on the user's request. Follow best practices and modern patterns.`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt }
        ],
        max_tokens: options.maxTokens || 2000,
        temperature: options.temperature || 0.3
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI');
      }

      return content;
    } catch (error) {
      Logger.error('Failed to generate code:', error);
      throw new Error(`Code generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Analyze image using AI vision
   */
  async analyzeImage(imageUrl: string, prompt: string): Promise<string> {
    if (!this.openai) {
      throw new Error('OpenAI not initialized. Please check your API key.');
    }

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              { type: 'image_url', image_url: { url: imageUrl } }
            ]
          }
        ],
        max_tokens: 4000
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from AI vision');
      }

      return content;
    } catch (error) {
      Logger.error('Failed to analyze image:', error);
      throw new Error(`Image analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
