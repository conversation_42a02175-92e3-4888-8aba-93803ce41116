import { TestRunner, TestRunOptions } from './TestRunner';
import { createDesignSystemAnalyzerTests } from './services/DesignSystemAnalyzer.test';
import { createTaskPlannerTests } from './services/TaskPlanner.test';
import { Logger } from '../utils/Logger';

export class TestSuiteRunner {
  private testRunner: TestRunner;

  constructor() {
    this.testRunner = new TestRunner();
    this.registerAllTestSuites();
  }

  /**
   * Run all test suites
   */
  async runAllTests(options: TestRunOptions = {}): Promise<void> {
    Logger.info('🧪 Starting UIOrbit Test Suite...');
    
    try {
      const results = await this.testRunner.runAll({
        verbose: true,
        bail: false,
        timeout: 30000,
        ...options
      });

      // Generate test report
      this.generateTestReport(results);

      // Check if all tests passed
      const allPassed = results.every(suite => suite.status === 'passed');
      
      if (allPassed) {
        Logger.info('✅ All tests passed!');
      } else {
        Logger.error('❌ Some tests failed!');
        throw new Error('Test suite failed');
      }

    } catch (error) {
      Logger.error('Test suite execution failed:', error);
      throw error;
    }
  }

  /**
   * Run tests for a specific service
   */
  async runServiceTests(serviceName: string, options: TestRunOptions = {}): Promise<void> {
    Logger.info(`🧪 Running tests for ${serviceName}...`);
    
    const results = await this.testRunner.runAll({
      pattern: serviceName,
      verbose: true,
      ...options
    });

    this.generateTestReport(results);
  }

  /**
   * Run smoke tests (basic functionality)
   */
  async runSmokeTests(): Promise<void> {
    Logger.info('🧪 Running smoke tests...');
    
    const smokeTestOptions: TestRunOptions = {
      timeout: 10000,
      bail: true,
      verbose: false
    };

    // Run a subset of critical tests
    await this.runServiceTests('DesignSystemAnalyzer', smokeTestOptions);
    await this.runServiceTests('TaskPlanner', smokeTestOptions);
    
    Logger.info('✅ Smoke tests completed');
  }

  /**
   * Get test coverage report
   */
  async getCoverageReport(): Promise<any> {
    return await this.testRunner.getCoverageReport();
  }

  private registerAllTestSuites(): void {
    Logger.info('Registering test suites...');

    // Core Services Tests
    this.testRunner.registerSuite(createDesignSystemAnalyzerTests());
    this.testRunner.registerSuite(createTaskPlannerTests());

    // Add more test suites here as they are created
    this.testRunner.registerSuite(this.createAIServiceTests());
    this.testRunner.registerSuite(this.createFileOperationsTests());
    this.testRunner.registerSuite(this.createProgressReportingTests());
    this.testRunner.registerSuite(this.createIntegrationTests());

    Logger.info(`Registered ${this.testRunner.getSuites().length} test suites`);
  }

  private generateTestReport(results: any[]): void {
    const totalSuites = results.length;
    const passedSuites = results.filter(r => r.status === 'passed').length;
    const failedSuites = results.filter(r => r.status === 'failed').length;

    const totalTests = results.reduce((sum, r) => sum + r.totalTests, 0);
    const passedTests = results.reduce((sum, r) => sum + r.passedTests, 0);
    const failedTests = results.reduce((sum, r) => sum + r.failedTests, 0);
    const skippedTests = results.reduce((sum, r) => sum + r.skippedTests, 0);

    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

    Logger.info('\n📊 Test Report Summary');
    Logger.info('='.repeat(50));
    Logger.info(`Test Suites: ${passedSuites}/${totalSuites} passed`);
    Logger.info(`Tests: ${passedTests}/${totalTests} passed, ${failedTests} failed, ${skippedTests} skipped`);
    Logger.info(`Duration: ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}s)`);
    Logger.info(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      Logger.info('\n❌ Failed Tests:');
      results.forEach(suite => {
        suite.results
          .filter((test: any) => test.status === 'failed')
          .forEach((test: any) => {
            Logger.error(`  ${suite.suiteName} > ${test.testName}`);
            Logger.error(`    ${test.error instanceof Error ? test.error.message : 'Unknown error'}`);
          });
      });
    }

    Logger.info('='.repeat(50));
  }

  private createAIServiceTests(): any {
    return {
      name: 'AIService',
      description: 'Tests for AI Service functionality',
      tests: [
        {
          name: 'should generate code successfully',
          description: 'Test basic code generation',
          test: async () => {
            // Mock test for AI service
            const mockResult = 'function Button() { return <button>Click me</button>; }';
            if (!mockResult.includes('Button')) {
              throw new Error('Generated code should contain Button component');
            }
          }
        },
        {
          name: 'should handle API errors gracefully',
          description: 'Test error handling for API failures',
          test: async () => {
            // Test error handling
            try {
              // Simulate API error
              throw new Error('API rate limit exceeded');
            } catch (error) {
              if (!(error instanceof Error) || !error.message.includes('rate limit')) {
                throw new Error('Should handle rate limit errors');
              }
            }
          }
        }
      ]
    };
  }

  private createFileOperationsTests(): any {
    return {
      name: 'FileOperations',
      description: 'Tests for File Operations Service',
      tests: [
        {
          name: 'should read files successfully',
          description: 'Test file reading functionality',
          test: async () => {
            // Mock file operations test
            const mockFileContent = 'export default function Component() {}';
            if (!mockFileContent.includes('Component')) {
              throw new Error('File content should be readable');
            }
          }
        },
        {
          name: 'should write files successfully',
          description: 'Test file writing functionality',
          test: async () => {
            // Mock file write test
            const writeSuccess = true;
            if (!writeSuccess) {
              throw new Error('File write should succeed');
            }
          }
        }
      ]
    };
  }

  private createProgressReportingTests(): any {
    return {
      name: 'ProgressReporting',
      description: 'Tests for Progress Reporting Service',
      tests: [
        {
          name: 'should start progress session',
          description: 'Test progress session creation',
          test: async () => {
            // Mock progress test
            const sessionId = 'test-session-123';
            if (!sessionId || sessionId.length === 0) {
              throw new Error('Progress session should have valid ID');
            }
          }
        },
        {
          name: 'should update progress correctly',
          description: 'Test progress updates',
          test: async () => {
            // Mock progress update test
            const progress = 50;
            if (progress < 0 || progress > 100) {
              throw new Error('Progress should be between 0 and 100');
            }
          }
        }
      ]
    };
  }

  private createIntegrationTests(): any {
    return {
      name: 'Integration',
      description: 'Integration tests for service interactions',
      tests: [
        {
          name: 'should integrate AI and file operations',
          description: 'Test AI service generating code and file operations saving it',
          test: async () => {
            // Mock integration test
            const generatedCode = 'const Component = () => <div>Hello</div>;';
            const fileSaved = true;
            
            if (!generatedCode || !fileSaved) {
              throw new Error('Integration should work end-to-end');
            }
          }
        },
        {
          name: 'should integrate task planning and execution',
          description: 'Test task planner creating plan and executor running it',
          test: async () => {
            // Mock task integration test
            const planCreated = true;
            const tasksExecuted = true;
            
            if (!planCreated || !tasksExecuted) {
              throw new Error('Task planning and execution should integrate');
            }
          }
        },
        {
          name: 'should integrate progress reporting with long operations',
          description: 'Test progress reporting during long-running operations',
          test: async () => {
            // Mock progress integration test
            const progressStarted = true;
            const progressUpdated = true;
            const progressCompleted = true;
            
            if (!progressStarted || !progressUpdated || !progressCompleted) {
              throw new Error('Progress reporting should integrate with operations');
            }
          }
        }
      ]
    };
  }
}

// Export singleton instance
export const testSuiteRunner = new TestSuiteRunner();
