<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .preview-header {
            padding: 16px;
            border-bottom: 1px solid var(--vscode-panel-border);
            background: var(--vscode-sideBar-background);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--vscode-foreground);
        }

        .preview-controls {
            display: flex;
            gap: 8px;
        }

        .control-button {
            padding: 6px 12px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
        }

        .control-button:hover {
            background: var(--vscode-button-hoverBackground);
        }

        .control-button.active {
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .preview-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .device-frame {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background: var(--vscode-editor-background);
        }

        .device-screen {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .device-screen.mobile {
            width: 375px;
            height: 667px;
        }

        .device-screen.tablet {
            width: 768px;
            height: 1024px;
        }

        .device-screen.desktop {
            width: 1200px;
            height: 800px;
        }

        .preview-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .device-info {
            position: absolute;
            top: -30px;
            left: 0;
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            background: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            padding: 4px 8px;
            border-radius: 4px;
        }

        .preview-tabs {
            display: flex;
            border-bottom: 1px solid var(--vscode-panel-border);
            background: var(--vscode-sideBar-background);
        }

        .preview-tab {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-size: 14px;
            transition: all 0.2s;
        }

        .preview-tab:hover {
            background: var(--vscode-list-hoverBackground);
        }

        .preview-tab.active {
            border-bottom-color: var(--vscode-button-background);
            color: var(--vscode-button-background);
        }

        .code-panel {
            display: none;
            flex: 1;
            overflow: hidden;
        }

        .code-panel.active {
            display: flex;
            flex-direction: column;
        }

        .code-editor {
            flex: 1;
            background: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            padding: 16px;
            overflow: auto;
            white-space: pre-wrap;
            border: none;
            outline: none;
            resize: none;
        }

        .empty-state {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 40px;
            color: var(--vscode-descriptionForeground);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--vscode-foreground);
        }

        .empty-description {
            font-size: 14px;
            margin-bottom: 24px;
            max-width: 400px;
        }

        .empty-actions {
            display: flex;
            gap: 12px;
        }

        .action-button {
            padding: 10px 20px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .action-button:hover {
            background: var(--vscode-button-hoverBackground);
        }

        .action-button.secondary {
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .action-button.secondary:hover {
            background: var(--vscode-button-secondaryHoverBackground);
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--vscode-descriptionForeground);
            border-radius: 50%;
            border-top-color: var(--vscode-button-background);
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .status-bar {
            padding: 8px 16px;
            background: var(--vscode-statusBar-background);
            color: var(--vscode-statusBar-foreground);
            font-size: 12px;
            border-top: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-left {
            display: flex;
            gap: 16px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
        }

        .status-indicator.error {
            background: #F44336;
        }

        .status-indicator.warning {
            background: #FF9800;
        }
    </style>
</head>
<body>
    <div class="preview-header">
        <div class="preview-title">🖼️ Component Preview</div>
        <div class="preview-controls">
            <button class="control-button active" data-device="mobile">📱 Mobile</button>
            <button class="control-button" data-device="tablet">📱 Tablet</button>
            <button class="control-button" data-device="desktop">🖥️ Desktop</button>
            <button class="control-button" onclick="refreshPreview()">🔄 Refresh</button>
            <button class="control-button" onclick="openInBrowser()">🌐 Browser</button>
        </div>
    </div>

    <div class="preview-tabs">
        <div class="preview-tab active" data-tab="preview">Preview</div>
        <div class="preview-tab" data-tab="code">Code</div>
        <div class="preview-tab" data-tab="props">Props</div>
    </div>

    <div class="preview-container">
        <!-- Preview Panel -->
        <div id="previewPanel" class="preview-panel active">
            <div class="device-frame">
                <div class="device-screen mobile" id="deviceScreen">
                    <div class="device-info">375 × 667</div>
                    <iframe id="previewIframe" class="preview-iframe" src="about:blank"></iframe>
                </div>
            </div>
        </div>

        <!-- Code Panel -->
        <div id="codePanel" class="code-panel">
            <textarea id="codeEditor" class="code-editor" placeholder="Component code will appear here..."></textarea>
        </div>

        <!-- Props Panel -->
        <div id="propsPanel" class="code-panel">
            <textarea id="propsEditor" class="code-editor" placeholder="Component props and documentation will appear here..."></textarea>
        </div>

        <!-- Empty State -->
        <div id="emptyState" class="empty-state">
            <div class="empty-icon">🎨</div>
            <div class="empty-title">No Component Selected</div>
            <div class="empty-description">
                Select a component from the chat or create a new one to see the live preview
            </div>
            <div class="empty-actions">
                <button class="action-button" onclick="createSampleComponent()">Create Sample Component</button>
                <button class="action-button secondary" onclick="openComponentLibrary()">Browse Components</button>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="loading" style="display: none;">
            <div class="loading-spinner"></div>
            Generating preview...
        </div>
    </div>

    <div class="status-bar">
        <div class="status-left">
            <div class="status-item">
                <div class="status-indicator" id="previewStatus"></div>
                <span id="previewStatusText">Ready</span>
            </div>
            <div class="status-item">
                <span id="componentInfo">No component loaded</span>
            </div>
        </div>
        <div class="status-right">
            <span id="lastUpdated">Never updated</span>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let currentDevice = 'mobile';
        let currentTab = 'preview';
        let currentComponent = null;

        // Device switching
        document.querySelectorAll('[data-device]').forEach(button => {
            button.addEventListener('click', () => {
                const device = button.dataset.device;
                switchDevice(device);
            });
        });

        // Tab switching
        document.querySelectorAll('[data-tab]').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                switchTab(tabName);
            });
        });

        function switchDevice(device) {
            currentDevice = device;
            
            // Update button states
            document.querySelectorAll('[data-device]').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.device === device);
            });

            // Update device screen
            const screen = document.getElementById('deviceScreen');
            const info = screen.querySelector('.device-info');
            
            screen.className = `device-screen ${device}`;
            
            switch (device) {
                case 'mobile':
                    info.textContent = '375 × 667';
                    break;
                case 'tablet':
                    info.textContent = '768 × 1024';
                    break;
                case 'desktop':
                    info.textContent = '1200 × 800';
                    break;
            }

            // Notify extension
            vscode.postMessage({
                type: 'deviceChanged',
                device: device
            });
        }

        function switchTab(tab) {
            currentTab = tab;
            
            // Update tab states
            document.querySelectorAll('.preview-tab').forEach(t => {
                t.classList.toggle('active', t.dataset.tab === tab);
            });

            // Show/hide panels
            document.getElementById('previewPanel').style.display = tab === 'preview' ? 'flex' : 'none';
            document.getElementById('codePanel').classList.toggle('active', tab === 'code');
            document.getElementById('propsPanel').classList.toggle('active', tab === 'props');
        }

        function refreshPreview() {
            if (currentComponent) {
                showLoading();
                vscode.postMessage({
                    type: 'refreshPreview',
                    component: currentComponent
                });
            }
        }

        function openInBrowser() {
            if (currentComponent) {
                vscode.postMessage({
                    type: 'openInBrowser',
                    component: currentComponent
                });
            }
        }

        function createSampleComponent() {
            vscode.postMessage({
                type: 'createSampleComponent'
            });
        }

        function openComponentLibrary() {
            vscode.postMessage({
                type: 'openComponentLibrary'
            });
        }

        function showLoading() {
            document.getElementById('loadingState').style.display = 'flex';
            document.getElementById('emptyState').style.display = 'none';
            document.getElementById('previewPanel').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
        }

        function showEmptyState() {
            document.getElementById('emptyState').style.display = 'flex';
            document.getElementById('previewPanel').style.display = 'none';
            hideLoading();
        }

        function showPreview() {
            document.getElementById('emptyState').style.display = 'none';
            document.getElementById('previewPanel').style.display = 'flex';
            hideLoading();
        }

        function updateStatus(status, text) {
            const indicator = document.getElementById('previewStatus');
            const statusText = document.getElementById('previewStatusText');
            
            indicator.className = `status-indicator ${status}`;
            statusText.textContent = text;
        }

        function updateComponentInfo(info) {
            document.getElementById('componentInfo').textContent = info;
        }

        function updateLastUpdated() {
            document.getElementById('lastUpdated').textContent = new Date().toLocaleTimeString();
        }

        // Listen for messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.type) {
                case 'loadComponent':
                    currentComponent = message.component;
                    loadComponent(message.component);
                    break;
                    
                case 'updatePreview':
                    updatePreview(message.html);
                    break;
                    
                case 'updateCode':
                    updateCode(message.code);
                    break;
                    
                case 'updateProps':
                    updateProps(message.props);
                    break;
                    
                case 'error':
                    updateStatus('error', message.message);
                    break;
            }
        });

        function loadComponent(component) {
            showLoading();
            updateComponentInfo(`Loading ${component.name}...`);
            updateStatus('', 'Loading...');
            
            // Request component data from extension
            vscode.postMessage({
                type: 'loadComponentData',
                component: component
            });
        }

        function updatePreview(html) {
            const iframe = document.getElementById('previewIframe');
            iframe.srcdoc = html;
            showPreview();
            updateStatus('', 'Ready');
            updateLastUpdated();
        }

        function updateCode(code) {
            document.getElementById('codeEditor').value = code;
        }

        function updateProps(props) {
            document.getElementById('propsEditor').value = props;
        }

        // Initialize
        showEmptyState();
        updateStatus('', 'Ready');
    </script>
</body>
</html>
