import * as vscode from 'vscode';
import { BaseWebviewProvider } from './BaseWebviewProvider';
import { ConfigurationService } from '../services/ConfigurationService';
import { UsageTrackingService } from '../services/UsageTrackingService';
import { Logger } from '../utils/Logger';

/**
 * Settings webview provider for UIOrbit extension
 * Provides configuration interface for the extension
 */
export class SettingsWebviewProvider extends BaseWebviewProvider {
  public static readonly viewType = 'uiorbit-settings';

  private configService: ConfigurationService;
  private usageTrackingService?: UsageTrackingService;

  constructor(
    extensionUri: vscode.Uri,
    configService: ConfigurationService,
    usageTrackingService?: UsageTrackingService
  ) {
    super(extensionUri, 'settings.html');
    this.configService = configService;
    this.usageTrackingService = usageTrackingService;
  }

  protected async handleMessage(message: any): Promise<void> {
    try {
      switch (message.type) {
        case 'loadSettings':
          await this.loadSettings();
          break;

        case 'saveSettings':
          await this.saveSettings(message.settings);
          break;

        case 'resetSettings':
          await this.resetSettings();
          break;

        default:
          Logger.warn(`Unknown message type: ${message.type}`);
      }
    } catch (error) {
      Logger.error('Error handling settings message:', error);
      this.showError('Failed to process settings request');
    }
  }

  protected onWebviewReady(): void {
    super.onWebviewReady();
    // Load initial settings and usage stats
    this.loadSettings();
    this.loadUsageStats();
  }

  private async loadSettings(): Promise<void> {
    try {
      const settings = {
        apiKey: this.configService.getOpenAIApiKey(),
        model: await this.configService.getConfiguration('model') || 'gpt-4',
        framework: this.configService.getDefaultFramework(),
        styling: this.configService.getDefaultStyling(),
        packageManager: await this.configService.getConfiguration('packageManager') || 'npm',
        accessibility: this.configService.isAccessibilityEnabled(),
        responsive: this.configService.isResponsiveDesignEnabled(),
        typescript: await this.configService.getConfiguration('typescript') !== 'false',
        autoIndex: await this.configService.getConfiguration('autoIndex') !== 'false'
      };

      this.postMessage({
        type: 'settingsLoaded',
        settings: settings
      });

      // Update API status
      this.updateApiStatus();

    } catch (error) {
      Logger.error('Error loading settings:', error);
      this.showError('Failed to load settings');
    }
  }

  private async saveSettings(settings: any): Promise<void> {
    try {
      // Save to VS Code configuration
      const config = vscode.workspace.getConfiguration();
      
      await config.update('uiorbit.openaiApiKey', settings.apiKey, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.model', settings.model, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.defaultFramework', settings.framework, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.defaultStyling', settings.styling, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.packageManager', settings.packageManager, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.enableAccessibility', settings.accessibility, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.enableResponsiveDesign', settings.responsive, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.typescript', settings.typescript, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.autoIndex', settings.autoIndex, vscode.ConfigurationTarget.Global);

      this.showSuccess('Settings saved successfully');
      this.updateApiStatus();

      Logger.info('Settings saved successfully');

    } catch (error) {
      Logger.error('Error saving settings:', error);
      this.showError('Failed to save settings');
    }
  }

  private async resetSettings(): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration();
      
      // Reset to default values
      await config.update('uiorbit.openaiApiKey', undefined, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.model', 'gpt-4', vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.defaultFramework', 'react', vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.defaultStyling', 'tailwind', vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.packageManager', 'npm', vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.enableAccessibility', true, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.enableResponsiveDesign', true, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.typescript', true, vscode.ConfigurationTarget.Global);
      await config.update('uiorbit.autoIndex', true, vscode.ConfigurationTarget.Global);

      this.showSuccess('Settings reset to defaults');
      await this.loadSettings(); // Reload settings

      Logger.info('Settings reset to defaults');

    } catch (error) {
      Logger.error('Error resetting settings:', error);
      this.showError('Failed to reset settings');
    }
  }

  private async loadUsageStats(): Promise<void> {
    try {
      if (!this.usageTrackingService) {
        return;
      }

      const stats = await this.usageTrackingService.getUsageStats();
      
      this.postMessage({
        type: 'usageStats',
        stats: {
          requestsUsed: stats.current,
          requestsLimit: stats.limit,
          componentsGenerated: 0, // TODO: Add component tracking
          projectsCreated: 0 // TODO: Add project tracking
        }
      });

    } catch (error) {
      Logger.error('Error loading usage stats:', error);
    }
  }

  private async updateApiStatus(): Promise<void> {
    try {
      const apiKey = this.configService.getOpenAIApiKey();
      
      if (!apiKey) {
        this.postMessage({
          type: 'apiStatus',
          status: 'disconnected'
        });
        return;
      }

      // Test API connection (simplified)
      if (apiKey.startsWith('sk-') && apiKey.length > 20) {
        this.postMessage({
          type: 'apiStatus',
          status: 'connected'
        });
      } else {
        this.postMessage({
          type: 'apiStatus',
          status: 'error'
        });
      }

    } catch (error) {
      Logger.error('Error checking API status:', error);
      this.postMessage({
        type: 'apiStatus',
        status: 'error'
      });
    }
  }

  /**
   * Public method to refresh usage stats
   */
  public refreshUsageStats(): void {
    this.loadUsageStats();
  }

  /**
   * Public method to update API status
   */
  public refreshApiStatus(): void {
    this.updateApiStatus();
  }
}
