import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { Logger } from '../utils/Logger';
import { ServiceRegistry } from '../core/ServiceRegistry';
import { FileOperationsService } from './FileOperationsService';

export interface ColorPalette {
  primary: string[];
  secondary: string[];
  accent: string[];
  neutral: string[];
  semantic: {
    success: string[];
    warning: string[];
    error: string[];
    info: string[];
  };
}

export interface Typography {
  fontFamilies: {
    primary: string;
    secondary?: string;
    monospace: string;
  };
  fontSizes: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
  };
  fontWeights: {
    light: number;
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
  };
  lineHeights: {
    tight: number;
    normal: number;
    relaxed: number;
  };
}

export interface SpacingSystem {
  scale: 'linear' | 'exponential' | 'custom';
  baseUnit: number;
  values: {
    [key: string]: string;
  };
}

export interface Breakpoints {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
}

export interface ShadowSystem {
  sm: string;
  base: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
}

export interface DesignTokens {
  colors: ColorPalette;
  typography: Typography;
  spacing: SpacingSystem;
  breakpoints: Breakpoints;
  shadows: ShadowSystem;
  borderRadius: {
    none: string;
    sm: string;
    base: string;
    md: string;
    lg: string;
    xl: string;
    full: string;
  };
  animations: {
    duration: {
      fast: string;
      normal: string;
      slow: string;
    };
    easing: {
      linear: string;
      easeIn: string;
      easeOut: string;
      easeInOut: string;
    };
  };
}

export interface ComponentPattern {
  name: string;
  type: 'atomic' | 'molecular' | 'organism' | 'template';
  variants: ComponentVariant[];
  usage: UsagePattern[];
  dependencies: string[];
  designTokens: string[];
  accessibility: A11yFeatures;
  responsive: boolean;
  interactive: boolean;
}

export interface ComponentVariant {
  name: string;
  props: { [key: string]: any };
  styles: { [key: string]: string };
  description: string;
}

export interface UsagePattern {
  context: string;
  frequency: number;
  bestPractices: string[];
  antiPatterns: string[];
}

export interface A11yFeatures {
  ariaLabels: boolean;
  keyboardNavigation: boolean;
  screenReaderSupport: boolean;
  colorContrast: 'AA' | 'AAA' | 'fail';
  focusManagement: boolean;
}

export interface DesignSystemAnalysis {
  tokens: DesignTokens;
  components: ComponentPattern[];
  patterns: {
    layout: string[];
    navigation: string[];
    forms: string[];
    feedback: string[];
  };
  consistency: {
    score: number;
    issues: string[];
    recommendations: string[];
  };
  modernization: {
    score: number;
    suggestions: string[];
    migrations: string[];
  };
}

export class DesignSystemAnalyzer {
  private fileOps: FileOperationsService;

  constructor(private serviceRegistry: ServiceRegistry) {
    this.fileOps = serviceRegistry.get<FileOperationsService>('fileOperations')!;
  }

  async analyzeWorkspaceDesignSystem(): Promise<DesignSystemAnalysis> {
    Logger.info('Starting design system analysis...');

    try {
      const styleFiles = await this.findStyleFiles();
      const componentFiles = await this.findComponentFiles();

      const tokens = await this.extractDesignTokens(styleFiles);
      const components = await this.analyzeComponents(componentFiles);
      const patterns = await this.identifyPatterns(componentFiles);
      const consistency = await this.analyzeConsistency(tokens, components);
      const modernization = await this.analyzeModernization(tokens, components);

      const analysis: DesignSystemAnalysis = {
        tokens,
        components,
        patterns,
        consistency,
        modernization
      };

      Logger.info('Design system analysis completed');
      return analysis;

    } catch (error) {
      Logger.error('Design system analysis failed:', error);
      throw new Error(`Failed to analyze design system: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async extractDesignTokens(styleFiles: string[]): Promise<DesignTokens> {
    Logger.info('Extracting design tokens...');

    const colors = await this.extractColors(styleFiles);
    const typography = await this.extractTypography(styleFiles);
    const spacing = await this.extractSpacing(styleFiles);
    const breakpoints = await this.extractBreakpoints(styleFiles);
    const shadows = await this.extractShadows(styleFiles);
    const borderRadius = await this.extractBorderRadius(styleFiles);
    const animations = await this.extractAnimations(styleFiles);

    return {
      colors,
      typography,
      spacing,
      breakpoints,
      shadows,
      borderRadius,
      animations
    };
  }

  private async findStyleFiles(): Promise<string[]> {
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) {
      return [];
    }

    const styleExtensions = ['.css', '.scss', '.sass', '.less', '.styl'];
    const files: string[] = [];

    const searchPattern = `**/*{${styleExtensions.join(',')}}`;
    const fileUris = await vscode.workspace.findFiles(searchPattern, '**/node_modules/**');

    for (const uri of fileUris) {
      files.push(uri.fsPath);
    }

    return files;
  }

  private async findComponentFiles(): Promise<string[]> {
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) {
      return [];
    }

    const componentExtensions = ['.tsx', '.jsx', '.vue', '.svelte'];
    const files: string[] = [];

    const searchPattern = `**/*{${componentExtensions.join(',')}}`;
    const fileUris = await vscode.workspace.findFiles(searchPattern, '**/node_modules/**');

    for (const uri of fileUris) {
      files.push(uri.fsPath);
    }

    return files;
  }

  private async extractColors(styleFiles: string[]): Promise<ColorPalette> {
    const colors: ColorPalette = {
      primary: [],
      secondary: [],
      accent: [],
      neutral: [],
      semantic: {
        success: [],
        warning: [],
        error: [],
        info: []
      }
    };

    for (const file of styleFiles) {
      try {
        const result = await this.fileOps.readFile(file);
        if (!result.success || !result.data) {
          continue;
        }
        const content = result.data;

        // Extract CSS custom properties (variables)
        const cssVarRegex = /--[\w-]+:\s*([^;]+);/g;
        let match;
        
        while ((match = cssVarRegex.exec(content)) !== null) {
          const value = match[1].trim();
          
          // Check if it's a color value
          if (this.isColorValue(value)) {
            this.categorizeColor(value, colors, match[0]);
          }
        }

        // Extract hex colors
        const hexRegex = /#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})/g;
        while ((match = hexRegex.exec(content)) !== null) {
          this.categorizeColor(match[0], colors);
        }

        // Extract rgb/rgba colors
        const rgbRegex = /rgba?\([^)]+\)/g;
        while ((match = rgbRegex.exec(content)) !== null) {
          this.categorizeColor(match[0], colors);
        }

        // Extract hsl/hsla colors
        const hslRegex = /hsla?\([^)]+\)/g;
        while ((match = hslRegex.exec(content)) !== null) {
          this.categorizeColor(match[0], colors);
        }

      } catch (error) {
        Logger.warn(`Failed to extract colors from ${file}:`, error);
      }
    }

    return colors;
  }

  private isColorValue(value: string): boolean {
    // Check for hex colors
    if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
      return true;
    }
    
    // Check for rgb/rgba
    if (/^rgba?\([^)]+\)$/.test(value)) {
      return true;
    }
    
    // Check for hsl/hsla
    if (/^hsla?\([^)]+\)$/.test(value)) {
      return true;
    }
    
    // Check for named colors
    const namedColors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'gray', 'black', 'white'];
    if (namedColors.includes(value.toLowerCase())) {
      return true;
    }
    
    return false;
  }

  private categorizeColor(color: string, palette: ColorPalette, context?: string): void {
    const lowerColor = color.toLowerCase();
    const lowerContext = context?.toLowerCase() || '';

    // Semantic colors
    if (lowerContext.includes('success') || lowerContext.includes('green')) {
      if (!palette.semantic.success.includes(color)) {
        palette.semantic.success.push(color);
      }
    } else if (lowerContext.includes('warning') || lowerContext.includes('yellow') || lowerContext.includes('orange')) {
      if (!palette.semantic.warning.includes(color)) {
        palette.semantic.warning.push(color);
      }
    } else if (lowerContext.includes('error') || lowerContext.includes('danger') || lowerContext.includes('red')) {
      if (!palette.semantic.error.includes(color)) {
        palette.semantic.error.push(color);
      }
    } else if (lowerContext.includes('info') || lowerContext.includes('blue')) {
      if (!palette.semantic.info.includes(color)) {
        palette.semantic.info.push(color);
      }
    }
    // Primary colors
    else if (lowerContext.includes('primary') || lowerContext.includes('brand')) {
      if (!palette.primary.includes(color)) {
        palette.primary.push(color);
      }
    }
    // Secondary colors
    else if (lowerContext.includes('secondary') || lowerContext.includes('accent')) {
      if (!palette.secondary.includes(color)) {
        palette.secondary.push(color);
      }
    }
    // Neutral colors
    else if (lowerContext.includes('gray') || lowerContext.includes('neutral') ||
             lowerColor.includes('gray') || lowerColor.includes('grey') ||
             lowerColor === '#000000' || lowerColor === '#ffffff' || lowerColor === 'black' || lowerColor === 'white') {
      if (!palette.neutral.includes(color)) {
        palette.neutral.push(color);
      }
    }
    // Default to accent if no clear category
    else {
      if (!palette.accent.includes(color)) {
        palette.accent.push(color);
      }
    }
  }

  private async extractTypography(styleFiles: string[]): Promise<Typography> {
    const typography: Typography = {
      fontFamilies: {
        primary: 'system-ui, -apple-system, sans-serif',
        monospace: 'monospace'
      },
      fontSizes: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem'
      },
      fontWeights: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      },
      lineHeights: {
        tight: 1.25,
        normal: 1.5,
        relaxed: 1.75
      }
    };

    for (const file of styleFiles) {
      try {
        const result = await this.fileOps.readFile(file);
        if (!result.success || !result.data) {
          continue;
        }
        const content = result.data;

        // Extract font-family declarations
        const fontFamilyRegex = /font-family:\s*([^;]+);/g;
        let match;

        while ((match = fontFamilyRegex.exec(content)) !== null) {
          const fontFamily = match[1].trim().replace(/['"]/g, '');
          if (fontFamily.includes('monospace') || fontFamily.includes('mono')) {
            typography.fontFamilies.monospace = fontFamily;
          } else if (!typography.fontFamilies.secondary) {
            typography.fontFamilies.secondary = fontFamily;
          } else {
            typography.fontFamilies.primary = fontFamily;
          }
        }

        // Extract font-size declarations
        const fontSizeRegex = /font-size:\s*([^;]+);/g;
        while ((match = fontSizeRegex.exec(content)) !== null) {
          const fontSize = match[1].trim();
          // Categorize font sizes based on common patterns
          this.categorizeFontSize(fontSize, typography.fontSizes);
        }

        // Extract font-weight declarations
        const fontWeightRegex = /font-weight:\s*([^;]+);/g;
        while ((match = fontWeightRegex.exec(content)) !== null) {
          const fontWeight = match[1].trim();
          this.categorizeFontWeight(fontWeight, typography.fontWeights);
        }

        // Extract line-height declarations
        const lineHeightRegex = /line-height:\s*([^;]+);/g;
        while ((match = lineHeightRegex.exec(content)) !== null) {
          const lineHeight = match[1].trim();
          this.categorizeLineHeight(lineHeight, typography.lineHeights);
        }

      } catch (error) {
        Logger.warn(`Failed to extract typography from ${file}:`, error);
      }
    }

    return typography;
  }

  private categorizeFontSize(fontSize: string, fontSizes: any): void {
    const numericValue = parseFloat(fontSize);

    if (fontSize.includes('rem')) {
      if (numericValue <= 0.75) fontSizes.xs = fontSize;
      else if (numericValue <= 0.875) fontSizes.sm = fontSize;
      else if (numericValue <= 1) fontSizes.base = fontSize;
      else if (numericValue <= 1.125) fontSizes.lg = fontSize;
      else if (numericValue <= 1.25) fontSizes.xl = fontSize;
      else if (numericValue <= 1.5) fontSizes['2xl'] = fontSize;
      else if (numericValue <= 1.875) fontSizes['3xl'] = fontSize;
      else fontSizes['4xl'] = fontSize;
    }
  }

  private categorizeFontWeight(fontWeight: string, fontWeights: any): void {
    const numericValue = parseInt(fontWeight);

    if (!isNaN(numericValue)) {
      if (numericValue <= 300) fontWeights.light = numericValue;
      else if (numericValue <= 400) fontWeights.normal = numericValue;
      else if (numericValue <= 500) fontWeights.medium = numericValue;
      else if (numericValue <= 600) fontWeights.semibold = numericValue;
      else fontWeights.bold = numericValue;
    } else {
      // Handle named font weights
      switch (fontWeight.toLowerCase()) {
        case 'light': fontWeights.light = 300; break;
        case 'normal': fontWeights.normal = 400; break;
        case 'medium': fontWeights.medium = 500; break;
        case 'semibold': fontWeights.semibold = 600; break;
        case 'bold': fontWeights.bold = 700; break;
      }
    }
  }

  private categorizeLineHeight(lineHeight: string, lineHeights: any): void {
    const numericValue = parseFloat(lineHeight);

    if (!isNaN(numericValue)) {
      if (numericValue <= 1.25) lineHeights.tight = numericValue;
      else if (numericValue <= 1.5) lineHeights.normal = numericValue;
      else lineHeights.relaxed = numericValue;
    }
  }

  private async extractSpacing(styleFiles: string[]): Promise<SpacingSystem> {
    const spacing: SpacingSystem = {
      scale: 'linear',
      baseUnit: 4,
      values: {}
    };

    const spacingValues = new Set<string>();

    for (const file of styleFiles) {
      try {
        const result = await this.fileOps.readFile(file);
        if (!result.success || !result.data) {
          continue;
        }
        const content = result.data;

        // Extract spacing-related properties
        const spacingRegex = /(margin|padding|gap|top|right|bottom|left):\s*([^;]+);/g;
        let match;

        while ((match = spacingRegex.exec(content)) !== null) {
          const value = match[2].trim();
          if (this.isSpacingValue(value)) {
            spacingValues.add(value);
          }
        }

        // Extract CSS custom properties for spacing
        const cssVarRegex = /--[\w-]*(?:space|spacing|margin|padding|gap)[\w-]*:\s*([^;]+);/g;
        while ((match = cssVarRegex.exec(content)) !== null) {
          const value = match[1].trim();
          if (this.isSpacingValue(value)) {
            spacingValues.add(value);
          }
        }

      } catch (error) {
        Logger.warn(`Failed to extract spacing from ${file}:`, error);
      }
    }

    // Convert spacing values to organized system
    const sortedValues = Array.from(spacingValues).sort((a, b) => {
      const aNum = parseFloat(a);
      const bNum = parseFloat(b);
      return aNum - bNum;
    });

    // Create spacing scale
    sortedValues.forEach((value, index) => {
      const key = this.generateSpacingKey(value, index);
      spacing.values[key] = value;
    });

    // Determine scale type
    spacing.scale = this.determineSpacingScale(sortedValues);

    return spacing;
  }

  private isSpacingValue(value: string): boolean {
    // Check for valid spacing units
    return /^\d+(\.\d+)?(px|rem|em|%)$/.test(value) &&
           !value.includes('100%') && // Exclude full width/height
           parseFloat(value) >= 0;
  }

  private generateSpacingKey(value: string, index: number): string {
    const numericValue = parseFloat(value);

    if (numericValue === 0) return '0';
    if (numericValue <= 4) return 'xs';
    if (numericValue <= 8) return 'sm';
    if (numericValue <= 16) return 'md';
    if (numericValue <= 24) return 'lg';
    if (numericValue <= 32) return 'xl';
    if (numericValue <= 48) return '2xl';
    if (numericValue <= 64) return '3xl';

    return `${index}`;
  }

  private determineSpacingScale(values: string[]): 'linear' | 'exponential' | 'custom' {
    if (values.length < 3) return 'custom';

    const numericValues = values.map(v => parseFloat(v)).filter(v => !isNaN(v));

    // Check for linear progression
    const differences = [];
    for (let i = 1; i < numericValues.length; i++) {
      differences.push(numericValues[i] - numericValues[i - 1]);
    }

    const avgDifference = differences.reduce((a, b) => a + b, 0) / differences.length;
    const isLinear = differences.every(diff => Math.abs(diff - avgDifference) < avgDifference * 0.3);

    if (isLinear) return 'linear';

    // Check for exponential progression
    const ratios = [];
    for (let i = 1; i < numericValues.length; i++) {
      if (numericValues[i - 1] !== 0) {
        ratios.push(numericValues[i] / numericValues[i - 1]);
      }
    }

    const avgRatio = ratios.reduce((a, b) => a + b, 0) / ratios.length;
    const isExponential = ratios.every(ratio => Math.abs(ratio - avgRatio) < avgRatio * 0.3);

    if (isExponential) return 'exponential';

    return 'custom';
  }

  private async extractBreakpoints(styleFiles: string[]): Promise<Breakpoints> {
    const breakpoints: Breakpoints = {
      xs: '475px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px'
    };

    for (const file of styleFiles) {
      try {
        const result = await this.fileOps.readFile(file);
        if (!result.success || !result.data) {
          continue;
        }
        const content = result.data;

        // Extract media queries
        const mediaQueryRegex = /@media[^{]*\((?:min-width|max-width):\s*([^)]+)\)/g;
        let match;

        const foundBreakpoints: string[] = [];

        while ((match = mediaQueryRegex.exec(content)) !== null) {
          const breakpoint = match[1].trim();
          if (this.isValidBreakpoint(breakpoint)) {
            foundBreakpoints.push(breakpoint);
          }
        }

        // Sort and categorize breakpoints
        const sortedBreakpoints = foundBreakpoints
          .map(bp => ({ value: bp, numeric: parseFloat(bp) }))
          .sort((a, b) => a.numeric - b.numeric);

        // Map to standard breakpoint names
        if (sortedBreakpoints.length > 0) {
          const keys = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'] as const;
          sortedBreakpoints.forEach((bp, index) => {
            if (index < keys.length) {
              breakpoints[keys[index]] = bp.value;
            }
          });
        }

      } catch (error) {
        Logger.warn(`Failed to extract breakpoints from ${file}:`, error);
      }
    }

    return breakpoints;
  }

  private isValidBreakpoint(value: string): boolean {
    return /^\d+(\.\d+)?(px|em|rem)$/.test(value);
  }

  private async extractShadows(styleFiles: string[]): Promise<ShadowSystem> {
    const shadows: ShadowSystem = {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
      '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
      inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)'
    };

    for (const file of styleFiles) {
      try {
        const result = await this.fileOps.readFile(file);
        if (!result.success || !result.data) {
          continue;
        }
        const content = result.data;

        // Extract box-shadow declarations
        const shadowRegex = /box-shadow:\s*([^;]+);/g;
        let match;

        const foundShadows: string[] = [];

        while ((match = shadowRegex.exec(content)) !== null) {
          const shadow = match[1].trim();
          if (shadow !== 'none') {
            foundShadows.push(shadow);
          }
        }

        // Categorize shadows by intensity
        foundShadows.forEach(shadow => {
          this.categorizeShadow(shadow, shadows);
        });

      } catch (error) {
        Logger.warn(`Failed to extract shadows from ${file}:`, error);
      }
    }

    return shadows;
  }

  private categorizeShadow(shadow: string, shadows: ShadowSystem): void {
    // Simple categorization based on blur radius
    const blurMatch = shadow.match(/(\d+)px[^,]*rgb/);
    if (blurMatch) {
      const blurRadius = parseInt(blurMatch[1]);

      if (blurRadius <= 2) shadows.sm = shadow;
      else if (blurRadius <= 3) shadows.base = shadow;
      else if (blurRadius <= 6) shadows.md = shadow;
      else if (blurRadius <= 15) shadows.lg = shadow;
      else if (blurRadius <= 25) shadows.xl = shadow;
      else shadows['2xl'] = shadow;
    }

    if (shadow.includes('inset')) {
      shadows.inner = shadow;
    }
  }

  private async extractBorderRadius(styleFiles: string[]): Promise<any> {
    const borderRadius = {
      none: '0px',
      sm: '0.125rem',
      base: '0.25rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem',
      full: '9999px'
    };

    for (const file of styleFiles) {
      try {
        const result = await this.fileOps.readFile(file);
        if (!result.success || !result.data) {
          continue;
        }
        const content = result.data;

        // Extract border-radius declarations
        const radiusRegex = /border-radius:\s*([^;]+);/g;
        let match;

        while ((match = radiusRegex.exec(content)) !== null) {
          const radius = match[1].trim();
          this.categorizeBorderRadius(radius, borderRadius);
        }

      } catch (error) {
        Logger.warn(`Failed to extract border radius from ${file}:`, error);
      }
    }

    return borderRadius;
  }

  private categorizeBorderRadius(radius: string, borderRadius: any): void {
    const numericValue = parseFloat(radius);

    if (radius.includes('rem')) {
      if (numericValue === 0) borderRadius.none = radius;
      else if (numericValue <= 0.125) borderRadius.sm = radius;
      else if (numericValue <= 0.25) borderRadius.base = radius;
      else if (numericValue <= 0.375) borderRadius.md = radius;
      else if (numericValue <= 0.5) borderRadius.lg = radius;
      else if (numericValue <= 0.75) borderRadius.xl = radius;
    } else if (radius.includes('px')) {
      if (numericValue === 0) borderRadius.none = radius;
      else if (numericValue <= 2) borderRadius.sm = radius;
      else if (numericValue <= 4) borderRadius.base = radius;
      else if (numericValue <= 6) borderRadius.md = radius;
      else if (numericValue <= 8) borderRadius.lg = radius;
      else if (numericValue <= 12) borderRadius.xl = radius;
      else if (numericValue >= 9999 || radius === '50%') borderRadius.full = radius;
    }
  }

  private async extractAnimations(styleFiles: string[]): Promise<any> {
    const animations = {
      duration: {
        fast: '150ms',
        normal: '300ms',
        slow: '500ms'
      },
      easing: {
        linear: 'linear',
        easeIn: 'ease-in',
        easeOut: 'ease-out',
        easeInOut: 'ease-in-out'
      }
    };

    for (const file of styleFiles) {
      try {
        const result = await this.fileOps.readFile(file);
        if (!result.success || !result.data) {
          continue;
        }
        const content = result.data;

        // Extract transition durations
        const durationRegex = /transition(?:-duration)?:\s*([^;]+);/g;
        let match;

        while ((match = durationRegex.exec(content)) !== null) {
          const duration = match[1].trim();
          this.categorizeAnimationDuration(duration, animations.duration);
        }

        // Extract transition timing functions
        const easingRegex = /transition-timing-function:\s*([^;]+);/g;
        while ((match = easingRegex.exec(content)) !== null) {
          const easing = match[1].trim();
          this.categorizeAnimationEasing(easing, animations.easing);
        }

      } catch (error) {
        Logger.warn(`Failed to extract animations from ${file}:`, error);
      }
    }

    return animations;
  }

  private categorizeAnimationDuration(duration: string, durations: any): void {
    const numericValue = parseFloat(duration);

    if (duration.includes('ms')) {
      if (numericValue <= 150) durations.fast = duration;
      else if (numericValue <= 300) durations.normal = duration;
      else durations.slow = duration;
    } else if (duration.includes('s')) {
      if (numericValue <= 0.15) durations.fast = duration;
      else if (numericValue <= 0.3) durations.normal = duration;
      else durations.slow = duration;
    }
  }

  private categorizeAnimationEasing(easing: string, easings: any): void {
    switch (easing.toLowerCase()) {
      case 'linear': easings.linear = easing; break;
      case 'ease-in': easings.easeIn = easing; break;
      case 'ease-out': easings.easeOut = easing; break;
      case 'ease-in-out': easings.easeInOut = easing; break;
    }
  }

  private async analyzeComponents(componentFiles: string[]): Promise<ComponentPattern[]> {
    // This will be implemented in the next part
    return [];
  }

  private async identifyPatterns(componentFiles: string[]): Promise<any> {
    // This will be implemented in the next part
    return {
      layout: [],
      navigation: [],
      forms: [],
      feedback: []
    };
  }

  private async analyzeConsistency(tokens: DesignTokens, components: ComponentPattern[]): Promise<any> {
    // This will be implemented in the next part
    return {
      score: 85,
      issues: [],
      recommendations: []
    };
  }

  private async analyzeModernization(tokens: DesignTokens, components: ComponentPattern[]): Promise<any> {
    // This will be implemented in the next part
    return {
      score: 90,
      suggestions: [],
      migrations: []
    };
  }
}
